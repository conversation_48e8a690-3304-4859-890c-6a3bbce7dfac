#include "wlt_bluetooth.h"
#include "wlt_krnl_api.h"
#include "elog.h"
#include "ble_gap.h"
#include "system.h"
#include "user_vfs.h"
#include "common.h"
#include "ble_gatt_uuid.h"
#include "ble_gatt.h"
#include "service.h"

#define ADV_BIND_OFFSET     8
#define ADV_SN_OFFSET       4
#define ADV_RSP_DID_OFFSET  2
#define ADV_MAC_OFFSET      18
#define ADV_FLAG_REPORT_OFFSET      24
#define ADV_FLAG_HOST_OFFSET        25

static unsigned char *mac_str = NULL;
static bool          m_adving = false;
static bool          m_connect=false;
static bool          m_binding=false;
static uint16_t adv_int_min = 320;  //320 * 0.625 = 200ms
static uint16_t adv_int_max = 320;  //320 * 0.625 = 200ms

/*boradcast refference : 
* http://wiki.chervon.com.cn/pages/viewpage.action?pageId=11423932
*/
/* 蓝牙广播包内容，最大31-3 Byte */
static uint8_t adv_data[] =
{
    0x19, 0xFF, 0x51, 0x46,
    0x00, 0x00, 0x00, 0x00, //sn code :0
    0x00, 0x00, 0x00, 0x00, //bind status, default 0, bind 4449
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //charater
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //MAC address
    0x02, //report flag, 默认未同步RTC
    0x00, //host flag
    /* add for dongle connect */
    0x00,
    0x00,
};

/* 扫描响应包内容，最大31-3 Byte */
static uint8_t scan_rsp_data[] =
{
    0x10,//0x10
    0x09, //0x09
    // 默认名称"Chervon_device "，长度15
    'C', 'h', 'e', 'r', 'v', 'o', 'n', '_', 'd', 'e', 'v', 'i', 'c', 'e', 's',
};

enum
{
    BLE_HANDLE_SERVICE,
    BLE_HANDLE_RX_DECLARATION,
    BLE_HANDLE_RX_VALUE,
    BLE_HANDLE_RX_CFG,
    BLE_HANDLE_RX_DESCRIPTION,
    BLE_HANDLE_TX_DECLARATION,
    BLE_HANDLE_TX_VALUE,
    BLE_HANDLE_TX_DESCRIPTION,
};

#define WLT_UUID16(uuid16)  {uuid16&0xff,(uuid16&0xff00)>>8}

#define SPPLE_DATAOUT_VALUE_LEN  256

#define SPPLE_DATAIN_VALUE_LEN  256

#define TH_SVC_UUID             {0xF0, 0xFF}        // Server UUID 0xFFF0
#define TH_RX_UUID              {0xF1, 0xFF}        // Characteristic UUID 0xFFF1, notify
#define TH_TX_UUID              {0xF2, 0xFF}        // Characteristic UUID 0xFFF2, write with no response

uint8_t th_service_uuid[] = TH_SVC_UUID;
uint8_t th_rx_uuid[] = TH_RX_UUID;
uint8_t th_tx_uuid[] = TH_TX_UUID;

uint16_t ble_gatt_read_callback(uint16_t connection_handle, uint16_t att_handle, uint8_t *buffer,uint16_t buffer_size);
int ble_gatt_write_callback(uint16_t connection_handle, uint16_t att_handle, uint8_t *buffer, uint16_t buffer_size);
extern void dev_exti_sleep(void);
extern void user_loop_callback(void);

static const ble_gatt_attribute_t user_attrbute_table[] =
    {
        /* 数据收发蓝牙服务 */
        [BLE_HANDLE_SERVICE] = {
            {BLE_UUID_SIZE_2, WLT_UUID16(BLE_GATT_PRIMARY_SERVICE_UUID)},
            BLE_GATT_PROP_READ,
            BLE_UUID_SIZE_2,
            (uint8_t *)th_service_uuid},
        /* 蓝牙发送数据特征值 */
        [BLE_HANDLE_RX_DECLARATION] = {
            {BLE_UUID_SIZE_2, WLT_UUID16(BLE_GATT_CHARACTER_UUID)},
            BLE_GATT_PROP_READ,
            0,
            NULL,
        },
        [BLE_HANDLE_RX_VALUE] = {
            {BLE_UUID_SIZE_2, TH_RX_UUID},
             BLE_GATT_PROP_NOTI, //BLE_GATT_PROP_READ | BLE_GATT_PROP_NOTI, // 只需要notify，不需要read
            // BLE_GATT_PROP_WRITE_CMD,
            SPPLE_DATAOUT_VALUE_LEN,
            NULL,
        },
        [BLE_HANDLE_RX_CFG] = {
            {BLE_UUID_SIZE_2, WLT_UUID16(BLE_GATT_CLIENT_CHAR_CFG_UUID)},  /* UUID */
            BLE_GATT_PROP_READ | BLE_GATT_PROP_WRITE, /* Permissions */
            2,                                        /* Max size of the value */
            NULL,
            /* Value of the attribute */ /* Can assign a buffer here, or can be assigned in the application by user */
        },
        /* 蓝牙接收数据特征值 */
        [BLE_HANDLE_TX_DECLARATION] = {
            {BLE_UUID_SIZE_2, WLT_UUID16(BLE_GATT_CHARACTER_UUID)},
            BLE_GATT_PROP_READ,
            0,
            NULL,
        },
        [BLE_HANDLE_TX_VALUE] = {
            {BLE_UUID_SIZE_2, TH_TX_UUID},
            BLE_GATT_PROP_WRITE_CMD,
            SPPLE_DATAIN_VALUE_LEN,
            NULL,
        },
};
#define SIZE_OF_DB sizeof(user_attrbute_table)
/* 蓝牙服务定义 */
static blestack_init_t blestack_init =
{
        ble_gatt_read_callback,
        ble_gatt_write_callback,
        SIZE_OF_DB,
        user_attrbute_table
};

/**********************************************
* BLE GAP
**********************************************/

/*  brief:
*   开启蓝牙广播
*/
static void user_adv_on(void)
{
   if(m_connect==true)
   {
        return;
   }
    wlt_gap_advertisements_enable(1);// 开启广播
    m_adving = true;
    // send_eq(EVENT_BLE_ADV_ON);
    // log_i("Adv on!");
}

/*  brief
*   停止蓝牙广播
*/
static void user_adv_stop(void)
{
//    if(!m_adving)
//        return;
    wlt_gap_advertisements_enable(0);       // 停止广播
    m_adving = false;
    // send_eq(EVENT_BLE_ADV_OFF);
    // log_i("Adv stop!");
}

static void user_adv_int_normal(void)
{
    uint16_t adv_int_min=320;
    uint16_t adv_int_max=320;
    wlt_gap_advertisements_set_params(adv_int_min, adv_int_max);
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
    wlt_gap_scan_response_set_data(sizeof(scan_rsp_data), scan_rsp_data);           // 设置扫描响应包内容
    user_adv_on();
}
static void user_adv_int_sleep(void)
{
    uint16_t adv_int_min=1600;
    uint16_t adv_int_max=1600;
    wlt_gap_advertisements_set_params(adv_int_min, adv_int_max);
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
    wlt_gap_scan_response_set_data(sizeof(scan_rsp_data), scan_rsp_data);           // 设置扫描响应包内容
    user_adv_on();
}
/*  brief:
*   修改广播间隔
*/
static void user_adv_int_normal_set(void)
{
    // ******灵活广播策略******
    // uint8_t is_fleet=0;
    // ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R,&is_fleet);
    // if(is_fleet == 1)
    // {
    //     return;
    // }
    user_adv_stop();
    com_wq_add(100,user_adv_int_normal,WQ_PARAMETER_ONCE);
}
static void user_adv_int_sleep_set(void)
{
    // ******灵活广播策略******
    // uint8_t is_fleet=0;
    // ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R,&is_fleet);
    // if(is_fleet == 1)
    // {
    //     return;
    // }
    user_adv_stop();
    com_wq_add(100,user_adv_int_sleep,WQ_PARAMETER_ONCE);
}


/*  brief
*   按键或其他方式触发绑定时，修改广播包数据
*/
void user_adv_binding(uint8_t bind)
{
    if(bind){
        m_binding=true;
        adv_data[ADV_BIND_OFFSET] = 0x44;
        adv_data[ADV_BIND_OFFSET+1] = 0x49;
    }else{
        m_binding=false;
        adv_data[ADV_BIND_OFFSET] = 0;
        adv_data[ADV_BIND_OFFSET+1] = 0;
    }
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
}


/*  brief
*   设置或清除广播包内的上报标识（记录标识）
*/
int adv_report_flag_set(bool flag)
{
    uint8_t is_fleet=0;
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R,&is_fleet);
    // if(is_fleet == 0)
    // {
    //     return;//不是商用子设备，不产生记录标识，直接退出;2024-11-13
    // }
    if(flag != false && is_fleet == 1){
        adv_data[ADV_FLAG_REPORT_OFFSET] |= (0x01);
    }else{
        adv_data[ADV_FLAG_REPORT_OFFSET] &= ~(0x01);
    }
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
    return 0;
}

/*  brief
*   设置或清除广播包内的RTC有效标识
*/
int adv_report_RTC_flag_set(bool flag)
{
    if(flag != false){
        adv_data[ADV_FLAG_REPORT_OFFSET] |= (0x01 << 1);
    }else{
        adv_data[ADV_FLAG_REPORT_OFFSET] &= ~(0x01 << 1);
    }
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
    return 0;
}

/*  brief
*   设置或清除广播包内的主机上电标识
*/
int adv_host_flag_set(bool flag)
{
    adv_data[ADV_FLAG_HOST_OFFSET] = flag;
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
    return 0;
}

void adv_mac_load(void)
{
    bd_addr_t blue_addr;
    wlt_ble_gap_addr_get(&blue_addr);
    adv_data[ADV_MAC_OFFSET + 0] = blue_addr.bd_addr0;
    adv_data[ADV_MAC_OFFSET + 1] = blue_addr.bd_addr1;
    adv_data[ADV_MAC_OFFSET + 2] = blue_addr.bd_addr2;
    adv_data[ADV_MAC_OFFSET + 3] = blue_addr.bd_addr3;
    adv_data[ADV_MAC_OFFSET + 4] = blue_addr.bd_addr4;
    adv_data[ADV_MAC_OFFSET + 5] = blue_addr.bd_addr5;
}

int get_mac_address(uint8_t * mac)
{
    if(mac == NULL){
        return -1;
    }
    bd_addr_t blue_addr;
    wlt_ble_gap_addr_get(&blue_addr);
    mac[0] = blue_addr.bd_addr5;
    mac[1] = blue_addr.bd_addr4;
    mac[2] = blue_addr.bd_addr3;
    mac[3] = blue_addr.bd_addr2;
    mac[4] = blue_addr.bd_addr1;
    mac[5] = blue_addr.bd_addr0;
    return 0;
}

void wq_update_connect_parameter(void){
    IOT_LOG_I("wq_update_connect_parameter.");
    wlt_gap_request_connection_parameter_update(bt_dev.handle_excute, 8, 32, 0, 200);       // 向主机发起连接参数更新
    wlt_ble_gatt_mtu_exchange_req(0);       // 协商MTU
}

static void bt_le_event_cb(bt_le_event_type_t bt_le_event_type, void *event_data)
{
    wlt_le_device_connection_data_t *wlt_le_device_connection_data;
    wlt_le_device_disconnection_data_t *wlt_le_device_disconnection_data;
    wlt_att_event_mtu_exchange_complete_data_t *wlt_att_event_mtu_exchange_complete_data;
    wlt_le_connection_updata_complete_data_t *wlt_le_connection_updata_complete_data;
    /* The parameters appear to be semi-valid, now check to see what  */
    /* type the incoming event is.                                    */
    switch (bt_le_event_type)
    {
    /* 与主机建立连接 */
    case WLT_LE_CONNECTION_COMPLETE:
        wlt_le_device_connection_data = (wlt_le_device_connection_data_t *)event_data;
        // bd_addr_to_str(wlt_le_device_connection_data->RemoteDevice, mac_str);
        mac_str = bd_address_to_str(wlt_le_device_connection_data->RemoteDevice);
        log_i("LE Connection,addr:%s,Handle:%d,interval:%d,latency:%d\r\n", mac_str, wlt_le_device_connection_data->connection_handle, wlt_le_device_connection_data->conn_interval, wlt_le_device_connection_data->conn_latency); //?????е?
        bt_dev.handle_excute = wlt_le_device_connection_data->connection_handle;
        com_wq_add(1500, wq_update_connect_parameter,WQ_PARAMETER_ONCE); //1.5s后更新参数
        send_eq(EVENT_BLE_CONNECTED);
        m_connect = true;
        user_adv_stop();
        break;
    
    /* 断开连接 */
    case WLT_LE_DISCONNECTION_COMPLETE:     
        IOT_LOG_I("+IND=BLEDISCONNECTED\r\n");
        wlt_le_device_disconnection_data = (wlt_le_device_disconnection_data_t *)event_data;
        log_i("LE Disonnection,Handle:%d,reason:%d", wlt_le_device_disconnection_data->connection_handle, wlt_le_device_disconnection_data->reason);
        m_connect = false;
        send_eq(EVENT_BLE_DISCONNECT);
        user_adv_on();                       // 重新开启广播
        break;
    
    /* MTU协商完成 */
    case WLT_ATT_EVENT_MTU_EXCHANGE_COMPLETE:
        wlt_att_event_mtu_exchange_complete_data = (wlt_att_event_mtu_exchange_complete_data_t *)event_data;
        log_i("LE exchange mtu,Handle:%d,mtu:%d", wlt_att_event_mtu_exchange_complete_data->connection_handle, wlt_att_event_mtu_exchange_complete_data->mtu);
        break;
    
    /* 连接参数更新完成 */
    case WLT_LE_CONNECTION_UPDATE_COMPLETE:
        wlt_le_connection_updata_complete_data = (wlt_le_connection_updata_complete_data_t *)event_data;
        log_i("LE Connection update,Handle:%d,interval:%d,latency:%d", wlt_le_connection_updata_complete_data->connection_handle, wlt_le_connection_updata_complete_data->conn_interval, wlt_le_connection_updata_complete_data->conn_latency);
        break;
    default:
        break;
    }
}

/* The following function is the main user interface thread.  It     */
/* opens the Bluetooth Stack and then drives the main user interface.*/
void user_ble_init(void)
{
    bd_addr_t blue_addr;
    adv_mac_load();
    wlt_gap_advertisements_set_params(adv_int_min, adv_int_max);
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_SN_R,&(adv_data[ADV_SN_OFFSET]));
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_DID_R,&(scan_rsp_data[ADV_RSP_DID_OFFSET]));
    wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
    wlt_gap_scan_response_set_data(sizeof(scan_rsp_data), scan_rsp_data);           // 设置扫描响应包内容
    user_adv_on();
    bt_set_le_event_callback(bt_le_event_cb);
    wlt_ble_gap_addr_get(&blue_addr);
    mac_str = bd_address_to_str(blue_addr);
    IOT_LOG_I("BD_ADDR:%s\r\n", mac_str);
}

/**********************************************
* BLE GATT
**********************************************/
uint16_t ble_gatt_read_callback(uint16_t connection_handle, uint16_t att_handle,
                                 uint8_t *buffer,uint16_t buffer_size)
{
    uint8_t ret = 0;
    IOT_LOG_I("ble_gatt_read_callback=%d",att_handle);
    switch (att_handle)
    {
    case BLE_HANDLE_RX_VALUE:
        memcpy(buffer, "CHAR1_VALUE", strlen("CHAR1_VALUE"));
        ret = strlen("CHAR1_VALUE");
        break;
    default:
        break;
    }
    return ret;
}

/* 通过蓝牙接收到数据的回调函数 */
int ble_gatt_write_callback(uint16_t connection_handle, uint16_t att_handle, 
                             uint8_t *buffer, uint16_t buffer_size)
{
    mq_msg_t msg;
    switch (att_handle)
    {
        case BLE_HANDLE_RX_CFG:
            IOT_LOG_I("BLE_HANDLE_RX_CFG");
            break;
        case BLE_HANDLE_TX_VALUE:
            // IOT_LOG_I("handle=0x%x. size=%d.",connection_handle, buffer_size);
            // IOT_LOG_HEX_DUMP("ttRX:", 8, buffer, buffer_size);    // 打印收到的数据
            if(init_msg(&msg,buffer,buffer_size) < 0){
                break;
            }
            msg.handle_connect = connection_handle;
            msg.handle_att = att_handle;
            msg.from = MSG_FROM_BLE;
            send_mq(&msg);
            break;
        default:
            IOT_LOG_I("recive: %d.",att_handle);
            break;
    }
    return 0;
}

int ble_init(void)
{
    return ble_gatt_init((void *)&blestack_init);
}

/**********************************************
* BLE dev options
**********************************************/
static int dev_open(dev_t* dev)
{
    if(dev == NULL){
        return -1;
    }
    if(!dev->has_init){
        //init bt & set callback
        wlt_ble_stack_init();   //蓝牙协议栈初始化
        ble_init();             //蓝牙服务初始化
        wlt_set_tx_power(CHIPTX_POWER_POS_4dBm);//发射功率max 10dBm,产品要求不大于4dBm.
        user_ble_init();        //广播包初始化
    }
    dev->ref ++;
    return dev->ref;
}

static int dev_close(dev_t* dev)
{
    if(dev == NULL){
        return -1;
    }
    dev->ref --;
    if(dev->ref <= 0){
        dev->has_init = false;
        dev->ref = 0;
    }
    return dev->ref;
}

static int dev_read(dev_t* dev, uint8_t* data)
{
    if(dev == NULL){
        return -1;
    }
    if(get_mq((mq_msg_t *)data) < 0)
    {
        IOT_LOG_I("get bt msg failed.");
        return -1;
    }
    return 0;
}

static int dev_write(dev_t* dev, uint8_t* data, int len)
{
    if(dev == NULL){
        return -1;
    }
    //real notify to app
    // IOT_LOG_I("bt replay : hand:0x%x, 0x%x, %d.",dev->handle_excute,data,len);
    wlt_ble_gatt_notify_data(dev->handle_excute, BLE_HANDLE_RX_VALUE, data, len);
    return 0;
}

static int dev_ioctl(dev_t* dev, int cmd, uint8_t *data)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    switch(cmd){
        case DEV_IOCTL_BLE_CMD_DISCONNECT:
            IOT_LOG_D("dev: disconnect ble: 0x%x.",dev->handle_excute);
            wlt_ble_gap_disconnect(dev->handle_excute); //disconnect current ble
        break;
        case DEV_IOCTL_BLE_CMD_ADV_ON:
            user_adv_on();
        break;
        case DEV_IOCTL_BLE_CMD_ADV_OFF:
            user_adv_stop();
        break;
        case DEV_IOCTL_BLE_CMD_ADV_BIND:
            user_adv_binding(1);
        break;
        case DEV_IOCTL_BLE_CMD_ADV_NORMAL:
            user_adv_binding(0);
        break;
        case DEV_IOCTL_BLE_CMD_ADV_HOST_ON:
            adv_host_flag_set(0);//主机已上电
        break;
        case DEV_IOCTL_BLE_CMD_ADV_HOST_OFF:
            adv_host_flag_set(1);//主机已断电
        break;
        case DEV_IOCTL_BLE_CMD_ADV_REPORT_ON:
            adv_report_flag_set(1);//有数据上报
        break;
        case DEV_IOCTL_BLE_CMD_ADV_REPORT_OFF:
            adv_report_flag_set(0);//无数据上报
        break;
        case DEV_IOCTL_BLE_CMD_ADV_RTC_VALID:
            adv_report_RTC_flag_set(0);//RTC数据有效
        break;
        case DEV_IOCTL_BLE_CMD_ADV_RTC_INVALID:
            adv_report_RTC_flag_set(1);//RTC数据无效
        break;
        case DEV_IOCTL_BLE_CMD_DID_WRITE://device id change
            ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_SN_R,&(adv_data[ADV_SN_OFFSET]));
            ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_DID_R,&(scan_rsp_data[ADV_RSP_DID_OFFSET]));
            wlt_gap_advertisements_set_data(sizeof(adv_data), adv_data);// 设置广播包内容
            wlt_gap_scan_response_set_data(sizeof(scan_rsp_data), scan_rsp_data);// 设置扫描响应包内容
        break;

        case DEV_IOCTL_BLE_CMD_GET_MAC: //获取mac地址
            ret = get_mac_address(data);
        break;
        case DEV_IOCTL_BLE_CMD_GET_CONN_STATUS:
            *data = (m_connect==true)?1:0;
        break;
        case DEV_IOCTL_BLE_CMD_GET_BIND_STATUS:
            *data = (m_binding==true)?1:0;
        break;
        case DEV_IOCTL_BLE_CMD_SET_NORMAL_ADV_GAP:
            user_adv_int_normal_set();
        break;
        case DEV_IOCTL_BLE_CMD_SET_SLEEP_ADV_GAP:
            user_adv_int_sleep_set();
        break;
        default:
            IOT_LOG_I("invalid cmd.%d.",cmd);
            ret = -1;
        break;
    }
    return ret;
}

/**********************************************
* BLE dev instance
**********************************************/
struct dev_ops bt_ops = {
    .open = dev_open,
    .close = dev_close,
    .read = dev_read,
    .write = dev_write,
    .ioctl = dev_ioctl
};

dev_t bt_dev = {
    .has_init = false,
    .ref = 0,
    .ops = &bt_ops
};


#ifndef __USER_COM_H__
#define __USER_COM_H__

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "wlt_krnl_api.h"
#include "elog.h"
#include "define.h"

#ifdef __cplusplus
extern "C" {
#endif

//WLT elog Define
#define LOG_SYSTEM_ENABLE   0x01
#define LOG_SYSTEM_DISABLE  0x02
#define LOG_SYSTEM          LOG_SYSTEM_ENABLE
#if (LOG_SYSTEM == LOG_SYSTEM_ENABLE)
#define LOG_SYSTEM_SWITCH  1
#endif
#if (LOG_SYSTEM == LOG_SYSTEM_DISABLE)
#define LOG_SYSTEM_SWITCH  0
#endif


//MSDK LOG Define, base WLT elog Define
#if IOT_LOG_ON
#define IOT_LOG_D(fmt,...)      log_d(("[debug]" fmt ), ##__VA_ARGS__)
#define IOT_LOG_I(fmt,...)      log_i(("[info]" fmt ),##__VA_ARGS__)
#define IOT_LOG_W(fmt,...)      log_w(("[warning]" fmt ),##__VA_ARGS__)
#define IOT_LOG_E(fmt,...)      log_e(("[error]" fmt ),##__VA_ARGS__)
//const char *name, uint8_t width, uint8_t *buf, uint16_t size
#define IOT_LOG_HEX_DUMP(...)   elog_hexdump(__VA_ARGS__)
#else
#define IOT_LOG_D(fmt,...)
#define IOT_LOG_I(fmt,...)
#define IOT_LOG_W(fmt,...)
#define IOT_LOG_E(fmt,...)
#define IOT_LOG_HEX_DUMP(...)
#define IOT_LOG_HEX_(...)       elog_hexdump(__VA_ARGS__)
#endif


//---------message queue-------------
#define MQ_HANDLE_T mailbox_t

typedef struct mq_msg_struct {
    uint16_t len;
    uint8_t from; //use MSG_FROM_XX
    uint8_t data[MQ_MSG_SIZE];
    uint16_t handle_connect;
    uint16_t handle_att;
}mq_msg_t;

MQ_HANDLE_T init_mq(uint16_t nums);
int destory_mq(void);
int init_msg(mq_msg_t* msg, uint8_t *data, uint16_t len);
int send_mq(mq_msg_t* msg);
int get_mq(mq_msg_t* msg);
int query_mq(void);

//---------event queue-------------
#define EQ_HANDLE_T mailbox_t

MQ_HANDLE_T init_eq(uint16_t nums);
int destory_eq(void);
int send_eq(uint8_t event);
int get_eq(void);
int query_eq(void);

//---------work queue----------------
typedef struct wq_element_struct{
    uint8_t use;
    uint32_t time;
    uint32_t period;
    uint32_t parameter;
    void_cb_func_t cb;
}wq_element_t;

typedef struct wq_data_struct{
    uint32_t tick;
    wq_element_t queue[WQ_ELEMENT_NUM];
}wq_data_t;

int com_wq_init(void);
void com_wq_tick_count(void);
int com_wq_init_from_sleep(void);
int com_wq_deinit_to_sleep(void);
int com_wq_query(void_cb_func_t cb);
int com_wq_add(uint32_t time,void_cb_func_t cb,uint32_t para);
int com_wq_delete(void_cb_func_t cb);
void com_wq_process(void);

//------------CRC-----------------
uint16_t com_crc16_xmodem(uint8_t *ptr,uint32_t len,uint16_t base);
uint16_t com_crc16_xmodem_init0(uint8_t *ptr,uint32_t len);

//------------Ending-----------------
uint32_t com_ending_32_unpack(uint8_t is_small, uint8_t *data);
void com_ending_32_pack(uint8_t is_small, uint32_t source, uint8_t *data);
uint16_t com_ending_16_unpack(uint8_t is_small, uint8_t *data);
void com_ending_16_pack(uint8_t is_small, uint16_t source, uint8_t *data);


#ifdef __cplusplus
}
#endif

#endif /* __USER_COM_H__ */

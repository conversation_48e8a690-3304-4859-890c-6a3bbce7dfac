#ifndef __TIMER_H__
#define __TIMER_H__

#include "sys_utils.h"

enum timer_channel
{
    TIMER_0,
    TIMER_1,
};

typedef void (*timer_callback)(void);

/**
 * @brief   启动定时器
 * 
 * @param   chan                    - TIMER_0 / TIMER_1
 * 
 * @return  空
 */
void dev_timer_run(enum timer_channel chan);

/**
 * @brief   停止定时器
 * 
 * @param   chan                    - TIMER_0 / TIMER_1
 * 
 * @return  空
 */
void dev_timer_stop(enum timer_channel chan);

/**
 * @brief   定时器初始化
 * 
 * @param   chan                    - TIMER_0 / TIMER_1
 * @param   count_us                - 定时器时间：100us - 349ms
 * 
 * @return  ture / false
 */
uint8_t dev_timer_init(enum timer_channel chan, uint32_t count_us);

/**
 * @brief   定时器回调函数设置
 * 
 * @param   chan                    - TIMER_0 / TIMER_1
 * @param   timer_callback_temp     - 回调函数
 * 
 * @return  空
 */
void dev_set_timer_callback(enum timer_channel chan,timer_callback timer_callback_temp);

/**
 * @brief   获取当前计数值
 * 
 * @param   chan                    - TIMER_0 / TIMER_1
 * 
 * @return  当前计数值
 */
uint32_t dev_get_current_count(enum timer_channel chan);

/**
 * @brief   获取当前装载值
 * 
 * @param   chan                    - TIMER_0 / TIMER_1
 * 
 * @return  当前装载值
 */
uint32_t dev_get_load_value(enum timer_channel chan);
#endif /* __TIMER_H__ */


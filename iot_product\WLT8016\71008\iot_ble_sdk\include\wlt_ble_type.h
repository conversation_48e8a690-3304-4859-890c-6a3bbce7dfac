/**
 * Copyright (c) 2021, wi-linktech
 * 
 * All rights reserved.
 * 
 * 
 */

#ifndef WLT_BLE_TYPE_H__
#define WLT_BLE_TYPE_H__

#define WLT_BLE_USE_STDLIB 1

#if WLT_BLE_USE_STDLIB

#include <stdio.h>
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <stddef.h>

#else

typedef unsigned char uint8_t;
typedef signed char int8_t;

typedef unsigned short uint16_t;
typedef signed short int16_t;

typedef int int32_t;
typedef unsigned int uint32_t;

typedef long long int64_t;
typedef unsigned long long uint64_t;

#define bool
#define true 1
#define false 0

#endif

typedef enum
{
    WLT_BLE_SUCCESS = 0x00,
    WLT_BLE_ERR_INTERNAL,
    WLT_BLE_ERR_NOT_FOUND,
    WLT_BLE_ERR_NO_EVENT,
    WLT_BLE_ERR_NO_MEM,
    WLT_BLE_ERR_INVALID_ADDR,  // Invalid pointer supplied
    WLT_BLE_ERR_INVALID_PARAM, // Invalid parameter(s) supplied.
    WLT_BLE_ERR_INVALID_STATE, // Invalid state to perform operation.
    WLT_BLE_ERR_INVALID_LENGTH,
    WLT_BLE_ERR_DATA_SIZE,
    WLT_BLE_ERR_TIMEOUT,
    WLT_BLE_ERR_BUSY,
    WLT_BLE_ERR_COMMON,
    WLT_BLE_ERR_RESOURCES,
    WLT_BLE_ERR_UNKNOWN, // other ble sdk errors
} wlt_ble_status_t;
#endif

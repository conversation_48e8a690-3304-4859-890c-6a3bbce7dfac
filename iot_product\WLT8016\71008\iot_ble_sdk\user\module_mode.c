#include "common.h"
#include "module.h"
#include "user_vfs.h"


/**************************
* 关于模式控制的逻辑
**************************/

#define MODULE_LISTENER     (18)

#define IOT_STATUS_NOBIND_NOCONN    (1)
#define IOT_STATUS_BIND_NOCONN      (1)
#define IOT_STATUS_CONN             (2)

#define IOT_UNIVERSAL_OFF           (0)
#define IOT_UNIVERSAL_ON            (1)
#define MCU_CONNECT_OFF             (0)
#define MCU_CONNECT_ON              (1)

#define MCU_CONNECT_STEP_HEART      (0)
#define MCU_CONNECT_STEP_INFO       (1)
#define MCU_CONNECT_STEP_MODE       (2)

#define HEART_BEAT_GAP3             (3000) //3s
#define HEART_BEAT_GAP10            (10000) //10s
#define HEART_BEAT_TIMEOUT          (3000) //3s
#define HEART_BEAT_TIMEOUT_TIMES    (3) //超过3次则判定MCU离线

#define ACTIVE_ADV_CHECK_GAP        (1000) //1s
#define WAKEUP_IO_CHECK_GAP         (1000) //1s
#define SLEEP_CHECK_GAP             (1000) //1s
#define SLEEP_WAIT_TIME             (10000) //10s

#define BAT_AD_CHECK_GAP10          (10000) //10s
#define BAT_AD_CHECK_GAP60          (60000) //60s

static mcu_version_info_t mcu_info; //缓存MCU上报的版本信息

static uint8_t connect_status = IOT_STATUS_NOBIND_NOCONN; //IOT状态,BLE连接情况
static uint8_t universal_status = IOT_UNIVERSAL_OFF; //透传状态
static uint8_t mcu_connect_status = MCU_CONNECT_OFF; //MCU连接状态
static uint8_t mcu_connect_step = MCU_CONNECT_STEP_HEART; //连接过程态
//static uint32_t heart_beat_gap = HEART_BEAT_GAP3; //心跳间隔
static uint8_t timeout_times = 1; //心跳超时次数
static uint8_t iap_flag = 0; //iap是否正在进行，1:正在进行，0:未进行
static uint8_t host_is_on = 0xff; //当前主机上电状态，0:未上电, 1:上电状态。当前值1不能改变匹配广播默认值

uint8_t factory_out=0;

/*************************************************
* global operations
*************************************************/
/*
* 透传模式是否开启;
* return : 1:开启, 0:关闭
*/
uint8_t module_is_universal_on(void)
{
    uint8_t onoff = 0;
    if(universal_status == IOT_UNIVERSAL_ON){
        onoff = 1;
    }
    return onoff;
}

/*
* 获取缓存的MCU version 信息
* return : 指针
*/
mcu_version_info_t* get_mcu_info_instance(void)
{
    return &mcu_info;
}

/*************************************************
* static base operations
*************************************************/

/*
* 发送心跳包 到MCU
*/
static void send_heartbeat(void)
{
    IOT_LOG_I("send_heartbeat.");
    proto_frame_t frame;
    frame.cmd = PROTO_CMD_HEART_BEAT;
    frame.len = 0;
    frame.pdata_origin = g_buf_MCU;
    protocol_pack(&frame);
    write(&uart_dev, g_buf_MCU, frame.len_origin);
}

/*
* 发送获取MCU信息 到MCU
*/
static void send_get_mcu_info(void)
{
    IOT_LOG_I("send_get_mcu_info.");
    proto_frame_t frame;
    frame.cmd = PROTO_CMD_GET_MCU_INFO;
    frame.len = 0;
    frame.pdata_origin = g_buf_MCU;
    protocol_pack(&frame);
    write(&uart_dev, g_buf_MCU, frame.len_origin);
}

/*
* 发送请求IOT模组状态 到MCU
*/
static void send_request_IOT_status(void)
{
    IOT_LOG_I("send_request_IOT_status.");
    proto_frame_t frame;
    frame.cmd = PROTO_CMD_REQUEST_IOT_MODE;
    frame.len = 0;
    frame.pdata_origin = g_buf_MCU;
    protocol_pack(&frame);
    write(&uart_dev, g_buf_MCU, frame.len_origin);
}

/*
* 发送IOT模组状态，到MCU；使用IOT_STATUS_XX
*/
static void send_IOT_status(uint8_t status)
{
    IOT_LOG_I("send_IOT_status.");
    proto_frame_t frame;
    uint8_t data = status;
    frame.cmd = PROTO_CMD_SEND_IOT_MODE;
    frame.len = 1;
    frame.pdata = &data;
    frame.pdata_origin = g_buf_MCU;
    protocol_pack(&frame);
    write(&uart_dev, g_buf_MCU, frame.len_origin);
}

/*
* 发送纽扣电池电压，到MCU
*/
static void send_bat_vol(uint8_t voltage)
{
    IOT_LOG_I("send_IOT_status.");
    proto_frame_t frame;
    uint8_t data = voltage;
    frame.cmd = PROTO_CMD_SEND_IOT_V;
    frame.len = 1;
    frame.pdata = &data;
    frame.pdata_origin = g_buf_MCU;
    protocol_pack(&frame);
    write(&uart_dev, g_buf_MCU, frame.len_origin);
}

/*
* 清空模组缓存数据
*/
static void clear_info(void)
{
    memset((void*)(&mcu_info),0,sizeof(mcu_version_info_t));
}

/*
* 发送主机带电状态通知，到APP； on:1; off:0
*/
static void send_host_status_to_app(void)
{
    IOT_LOG_I("send_IOT_status.");
    proto_frame_t frame;
    uint8_t data = (host_is_on)? 0:1; //0:主机正常，1:主机已断电
    frame.cmd = PROTO_CMD_HOST_STATE;
    frame.len = 1;
    frame.pdata = &data;
    frame.pdata_origin = g_buf_APP;
    protocol_pack(&frame);
    write(&bt_dev, g_buf_APP, frame.len_origin);
    // IOT_LOG_HEX_("d5:",16,g_buf_APP,frame.len_origin);
}

static void in_sleep_mode(void)
{
    IOT_LOG_I("in sleep mode.");
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_INSLEEP,NULL);
}

/*************************************************
* work queue operations
*************************************************/
static void heart_beat_service(void); //decleration
static void heart_beat_timeout(void);
static void wq_check_battery(void);

/*
* IAP开始，需要关闭心跳发送
*/
static void event_iap_start(void * data)
{
    /* 停止心跳发送 */
    iap_flag = 1;
    com_wq_delete(heart_beat_service);
    com_wq_delete(heart_beat_timeout);
    com_wq_delete(wq_check_battery);
}

/*
* IAP结束，需要开启心跳发送
*/
static void event_iap_end(void * data)
{
    /* 启动心跳发送 */
    iap_flag = 0;
    com_wq_add(HEART_BEAT_GAP3,heart_beat_service,WQ_PARAMETER_ONCE);
    com_wq_add(BAT_AD_CHECK_GAP10,wq_check_battery,WQ_PARAMETER_ONCE); //电池检测服务
}

/*
* 断开连接并重启
*/
static void reset(void)
{
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_RESET,NULL);
}

/*
* 断开连接并重启
*/
static void disconnect_reset(void)
{
    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_DISCONNECT,NULL);
    com_wq_add(100,reset,WQ_PARAMETER_ONCE); //100ms后重启
}

/*
* 心跳包接收超时处理
*/
static void heart_beat_timeout(void)
{
    com_wq_delete(heart_beat_service);
    send_heartbeat(); //发送心跳包到MCU
    com_wq_add(HEART_BEAT_TIMEOUT,heart_beat_timeout,WQ_PARAMETER_ONCE);//设置超时
    //heart_beat_gap = HEART_BEAT_TIMEOUT;
    timeout_times ++;
    if(timeout_times == HEART_BEAT_TIMEOUT_TIMES){
        //判定为MCU已离线
        //IOT_LOG_D("MCU not online.");
        timeout_times = 0;
        mcu_connect_status = MCU_CONNECT_OFF;
        mcu_connect_step = MCU_CONNECT_STEP_HEART;
    }
}

/*
* 心跳服务
*/
static void heart_beat_service(void)
{
    send_heartbeat(); //发送心跳包到MCU
    com_wq_add(HEART_BEAT_TIMEOUT,heart_beat_timeout,WQ_PARAMETER_ONCE);//设置超时
}


static void wq_check_wakeup_io(void)
{
    uint8_t status = 0;
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKEUPIO,&status);
    IOT_LOG_I("wq_check_wakeup_io: %d.",status);
    if(status == 1)
    {
        ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKE_MCU_OFF,NULL); //不唤醒MCU
    }
    
    if(host_is_on == status)
    {
        return;
    }
    host_is_on = status;
    if(host_is_on)
    {
        //主机上电状态
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_HOST_ON,NULL);
        send_eq(EVENT_HOST_ON);
        ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKE_MCU_OFF,NULL); //不唤醒MCU
        send_IOT_status(connect_status);
    }
    else
    {
        //主机处于未上电状态
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_HOST_OFF,NULL);
        send_eq(EVENT_HOST_OFF);

        if(connect_status == IOT_STATUS_CONN)
        {
            ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKE_MCU_ON,NULL); //使能唤醒MCU的端口
        }
    }
    //连接状态，需要发送主机上电状态通知到APP
    send_host_status_to_app();
}


static void wq_check_battery(void)
{
    uint8_t voltage_bat = 0;
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_BAT_VOL,&voltage_bat);
    send_bat_vol(voltage_bat);
    com_wq_add(BAT_AD_CHECK_GAP60,wq_check_battery,WQ_PARAMETER_ONCE); //端口检测服务
}



extern void sys_ble_adv_ctrl(void);

static void wq_check_sleep_parameter(void)
{
    if(iap_flag != 0){ //当前正在IAP升级，不要进入休眠
        if(com_wq_query(in_sleep_mode) >= 0){
            com_wq_delete(in_sleep_mode);
        }
        return;
    }
    if(connect_status != IOT_STATUS_CONN && host_is_on == 0){
        if(com_wq_query(in_sleep_mode) < 0){
            com_wq_add(SLEEP_WAIT_TIME,in_sleep_mode,WQ_PARAMETER_ONCE);
            com_wq_delete(sys_ble_adv_ctrl);
            ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_SET_SLEEP_ADV_GAP,NULL);
        }
    }else{
        com_wq_delete(in_sleep_mode);
        if(com_wq_query(sys_ble_adv_ctrl) < 0){
            com_wq_add(ACTIVE_ADV_CHECK_GAP,sys_ble_adv_ctrl,WQ_PARAMETER_CYCLE);
            ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_SET_NORMAL_ADV_GAP,NULL);
        }
    }
}

/*************************************************
* event operations
*************************************************/
static void connect_cb(void * data)
{
    IOT_LOG_I("enter.");
    connect_status = IOT_STATUS_CONN;
    send_IOT_status(connect_status);
    com_wq_add(2000,send_host_status_to_app,WQ_PARAMETER_CYCLE);

    if(host_is_on)//主机上电状态
    {
        ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKE_MCU_OFF,NULL); //BU使能唤醒MCU的端口
    }
    else
    {
        ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKE_MCU_ON,NULL); //使能唤醒MCU的端口
    }
}

static void host_statu_rcv_cb(void *data)
{
    com_wq_delete(send_host_status_to_app);
}

static void disconnect_cb(void * data)
{
    IOT_LOG_I("enter.");
    connect_status = IOT_STATUS_NOBIND_NOCONN;
    send_IOT_status(connect_status);
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKE_MCU_OFF,NULL); //非连接状态不使能唤醒MCU的端口
}

static void event_history_has_data(void * data)
{
    IOT_LOG_I("event_history_has_data.");
    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_REPORT_ON,NULL);
}

static void event_history_no_data(void * data)
{
    IOT_LOG_I("event_history_no_data.");
    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_REPORT_OFF,NULL);
}

/*************************************************
* cmd operations
*************************************************/
/*
* 接收到MCU回复的心跳包
*/
static void recive_heartbeat_cb(void * data)
{
    IOT_LOG_I("recive_heartbeat_cb.");
    proto_frame_t* pframe = data;
    if(pframe->pdata[0] == 0){ //提前处理心跳，mcu升级后要重新读取总成信息
        //MCU首次回复
        mcu_connect_step = MCU_CONNECT_STEP_HEART;
        mcu_connect_status = MCU_CONNECT_OFF;
    }
    if(iap_flag == 1){
        IOT_LOG_W("in iap. don't deal heartbeat.");
        return;
    }
    com_wq_delete(heart_beat_timeout);
    com_wq_add(HEART_BEAT_GAP10,heart_beat_service,WQ_PARAMETER_ONCE);
    //heart_beat_gap = HEART_BEAT_GAP10;
    timeout_times = 0;
    if(((mcu_connect_step==MCU_CONNECT_STEP_HEART)&&(mcu_connect_status==MCU_CONNECT_OFF))
        || mcu_connect_step==MCU_CONNECT_STEP_INFO)
    {
        mcu_connect_step = MCU_CONNECT_STEP_INFO;
        send_get_mcu_info();
    }
    else
    {
        send_IOT_status(connect_status);
    }
}

/*
* 接收到MCU回复的 info信息，需要缓存。然后回复ACK到MCU
*/
static void recive_mcu_info_cb(void * data)
{
    IOT_LOG_I("recive_mcu_info_cb.");
    proto_frame_t* pframe = data;
    uint8_t total = pframe->pdata[0]; //总个数
    uint8_t now = pframe->pdata[1]; //当前第几个
    uint8_t count = total;
    uint8_t index = now;
    if(total > MCU_INFO_NUM){
        IOT_LOG_W("MCU is too much.");
    }
    if(count > MCU_INFO_NUM){
        count = MCU_INFO_NUM;
    }
    if(index > MCU_INFO_NUM){
        index = MCU_INFO_NUM;
    }
    index = index - 1; //调整下标
    mcu_info.count = count;
    mcu_info.element[index].mcuid = pframe->pdata[2];
    memcpy(mcu_info.element[index].ver_hard,&(pframe->pdata[3]),MCU_VER_HARD_LEN);
    memcpy(mcu_info.element[index].ver_soft,&(pframe->pdata[3+MCU_VER_HARD_LEN]),MCU_VER_SOFT_LEN);
    IOT_LOG_D("get mcu info: id = 0x%x.",mcu_info.element[index].mcuid);
    IOT_LOG_HEX_DUMP("hard",16,mcu_info.element[index].ver_hard,MCU_VER_HARD_LEN);
    IOT_LOG_HEX_DUMP("soft",16,mcu_info.element[index].ver_soft,MCU_VER_SOFT_LEN);
    //判定接收完毕且步骤符合要求，跳转到下一个step
    if(mcu_connect_step == MCU_CONNECT_STEP_INFO && mcu_connect_status == MCU_CONNECT_OFF
        && total == now){
        mcu_connect_step = MCU_CONNECT_STEP_MODE;
        IOT_LOG_I("recive_mcu_info_cb.send mode check");
        send_request_IOT_status();
    }else{
        //回复ACK
        pframe->len = 2;
        pframe->pdata_origin = g_buf_MCU;
        protocol_pack(pframe);
        write(&uart_dev,g_buf_MCU,pframe->len_origin);
    }
}

/*
* 接收到请求模组工作模式, 回复连接情况
*/
static void recive_get_mode_cb(void * data)
{
    IOT_LOG_I("recive_get_mode_cb.");
    send_IOT_status(connect_status);
    //判定接收完毕且步骤符合要求，重置step并置位已连接
    if(mcu_connect_step == MCU_CONNECT_STEP_MODE && mcu_connect_status == MCU_CONNECT_OFF){
        mcu_connect_step = MCU_CONNECT_STEP_HEART;
        mcu_connect_status = MCU_CONNECT_ON;
    }
}

/*
* 接收到MCU请求模组重置，回复ACK到MCU 并清除缓存，断开连接，重启
*/
static void recive_reset_iotmodule_cb(void * data)
{
    IOT_LOG_I("recive_reset_iotmodule_cb.");
    proto_frame_t* pframe = data;
    memcpy(g_buf_MCU,pframe->pdata_origin,pframe->len_origin);
    write(&uart_dev, g_buf_MCU, pframe->len_origin);

    clear_info();
    com_wq_add(10,disconnect_reset,WQ_PARAMETER_ONCE);//10ms后断开连接
}

/*
* 接收到MCU请求的模组解绑/断连; 回复MCU断连结果到MCU
*/
static void recive_set_connect_cb(void * data)
{
    IOT_LOG_I("recive_set_connect_cb.");
    proto_frame_t* pframe = data;
    proto_frame_t frame;
    uint8_t result = 1; //return OK:1;
    frame.cmd = pframe->cmd;
    frame.len = 1;
    frame.pdata = &result;
    frame.pdata_origin = g_buf_MCU;
    protocol_pack(&frame);
    write(&uart_dev, g_buf_MCU, frame.len_origin);
}

/*
* 接收到APP的 透传数据指令，直接转发到MCU
*/
static void universal_cmd_cb(void * data)
{
    IOT_LOG_I("universal_cmd_cb.");
    proto_frame_t* pframe = data;
    memcpy(g_buf_MCU,pframe->pdata_origin,pframe->len_origin);
    write(&uart_dev, g_buf_MCU, pframe->len_origin);
}

/*
* 接收到APP的 透传指令开关设置，修改当前透传状态，并回复ACK到APP
*/
static void universal_onoff_cb(void * data)
{
    IOT_LOG_I("universal_onoff_cb.");
    proto_frame_t* pframe = data;
    if(pframe->len != 1){
        IOT_LOG_W("set universal status failed.");
        return;
    }
    universal_status = (pframe->pdata[0] == 1)?
                        IOT_UNIVERSAL_ON:IOT_UNIVERSAL_OFF;
    IOT_LOG_I("universal on off: %d.",universal_status);
    pframe->len = 0;
    pframe->pdata_origin = g_buf_APP;
    protocol_pack(pframe);
    write(&bt_dev, g_buf_APP, pframe->len_origin);
}

/*
* 接收到APP的唤醒设备指令，将MCU进行唤醒。不需要回复
*/
static void wakeup_device_cb(void * data)
{
    IOT_LOG_I("wakeup_device_cb.");
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKEMCU,NULL);
}

/*
* 接收到APP的 进入产测模式，并回复ACK到APP
*/
static void config_factory_test_mode_cb(void * data)
{
    IOT_LOG_I("config_factory_test_mode_cb.");
    proto_frame_t* pframe = data;
    int ret = 0;
    pframe->len = 1;
    pframe->pdata[0] = (ret<0)?1:0;
    pframe->pdata_origin = g_buf_APP;
    protocol_pack(pframe);
    write(&bt_dev, pframe->pdata_origin, pframe->len_origin);
}

/*
* 接收到APP的 进入出厂模式，并回复ACK到APP
*/
static void config_factory_out_stay_cb(void * data)
{
    IOT_LOG_I("config_factory_out_stay_cb.");
    proto_frame_t* pframe = data;
    pframe->len = 1;
    pframe->pdata[0] = 0;
    pframe->pdata_origin = g_buf_APP;
    protocol_pack(pframe);
    write(&bt_dev, pframe->pdata_origin, pframe->len_origin);

    factory_out = 1;
}

/*************************************************
* module operations
*************************************************/
/* event处理 
*  return: >=0 process; <0: no executed
*/
static int m_do_event(uint8_t event)
{
    int ret = -1;
    mlist_element_t* plist = module_mode.plist;
    for(int i = 0; i < MODULE_LISTENER; ++i){
        if(PARA_DECODE_EVENT(plist[i].para) == event){
            ret = event;
            plist[i].cb(&event);
        }
    }
    return ret;
}
/* msg处理 
*  return: >=0 process; <0: no executed
*/
static int m_do_cmd(proto_frame_t * pframe)
{
    int ret = -1;
    if(pframe == NULL){
        IOT_LOG_E("pframe is null.");
        return -1;
    }
    int cmd =  pframe->cmd;
    mlist_element_t* plist = module_mode.plist;
    for(int i = 0; i < MODULE_LISTENER; ++i){
        if(PARA_DECODE_EVENT(plist[i].para)>0){
            continue;
        }
        if(PARA_DECODE_CMD(plist[i].para) == cmd){
            ret = cmd;
            plist[i].cb(pframe);
        }
    }
    return ret;
}

/* 初始化 */
static void m_init(void)
{
    IOT_LOG_I("enter.");
    com_wq_add(HEART_BEAT_GAP3,heart_beat_service,WQ_PARAMETER_ONCE); //心跳服务
    com_wq_add(WAKEUP_IO_CHECK_GAP,wq_check_wakeup_io,WQ_PARAMETER_CYCLE); //端口检测服务
    com_wq_add(BAT_AD_CHECK_GAP10,wq_check_battery,WQ_PARAMETER_ONCE); //电池检测服务
    com_wq_add(ACTIVE_ADV_CHECK_GAP,sys_ble_adv_ctrl,WQ_PARAMETER_CYCLE); //adv ctrl

#if IOT_USE_SLEEP_MODE
    com_wq_add(SLEEP_CHECK_GAP,wq_check_sleep_parameter,WQ_PARAMETER_CYCLE); //休眠条件检测服务
#endif
}


static mlist_element_t module_list[MODULE_LISTENER] = {
{PARA_EVENT(EVENT_OTA_IAP_START),       event_iap_start},
{PARA_EVENT(EVENT_OTA_IAP_END),         event_iap_end},
{PARA_EVENT(EVENT_BLE_CONNECTED),       connect_cb},
{PARA_EVENT(EVENT_BLE_DISCONNECT),      disconnect_cb},
{PARA_EVENT(EVENT_HIS_HAS_DATA),        event_history_has_data},
{PARA_EVENT(EVENT_HIS_NO_DATA),         event_history_no_data},
{PARA_CMD(PROTO_CMD_UNIVERSAL_ONOFF),   universal_onoff_cb},
{PARA_CMD(PROTO_CMD_UNIVERSAL_DATA),    universal_cmd_cb},
{PARA_CMD(PROTO_CMD_REQUEST_IOT_MODE),  recive_get_mode_cb},
{PARA_CMD(PROTO_CMD_UNBIND),            recive_set_connect_cb},
{PARA_CMD(PROTO_CMD_CHECK_IOT_STATE),   recive_get_mode_cb},

{PARA_CMD(PROTO_CMD_HEART_BEAT),        recive_heartbeat_cb},
{PARA_CMD(PROTO_CMD_GET_MCU_INFO),      recive_mcu_info_cb},
{PARA_CMD(PROTO_CMD_RESET_IOT),         recive_reset_iotmodule_cb},
{PARA_CMD(PROTO_CMD_WAKEUP),            wakeup_device_cb},
{PARA_CMD(PROTO_CMD_HOST_STATE),        host_statu_rcv_cb},
{PARA_CMD(PROTO_CMD_FACTORY_TEST_MODE), config_factory_test_mode_cb},
{PARA_CMD(PROTO_CMD_FACTORY_OUT_STAY),  config_factory_out_stay_cb},
};

module_t module_mode = {
    .count = MODULE_LISTENER,
    .plist = module_list,
    .init = m_init,
    .do_event = m_do_event,
    .do_cmd = m_do_cmd,
};

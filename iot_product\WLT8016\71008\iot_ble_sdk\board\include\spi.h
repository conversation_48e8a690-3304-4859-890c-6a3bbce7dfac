#ifndef __SPI_H__
#define __SPI_H__

#include "sys_utils.h"

enum spi_mode
{
    SPI_ENTITY_MODE,    //spi master
    SPI_SHADOW_MODE,    //spi slaver
};

enum spi_sampling_mode
{
    SPI_CPOL_0_CPHA_0,    
    SPI_CPOL_0_CPHA_1,    
    SPI_CPOL_1_CPHA_0,    
    SPI_CPOL_1_CPHA_1,    
};

/**
 * @brief   SPI IO 初始化
 * 
 * @param   clk_pin                 - 时钟引脚
 * @param   csn_pin                 - 片选引脚
 * @param   dout_pin                - 输出引脚
 * @param   din_pin                 - 输入引脚
 * 
 * @return  空
 */
void dev_spi_io_init(uint16_t clk_pin, uint16_t csn_pin, uint16_t dout_pin, uint16_t din_pin);

/**
 * @brief   SPI 初始化
 * 
 * @param   bit_width               - 数据位宽：1-8
 * @param   entity_or_shadow        - 主或从：SPI_ENTITY_MODE/SPI_SHADOW_MODE
 * @param   bit_rate                - SPI速率
 * @param   cpol                    - SPI时钟极性
 * @param   cpha                    - SPI时钟相位
 * 
 * @return  空
 */
void dev_spi_init(uint8_t bit_width, uint8_t entity_or_shadow, uint32_t bit_rate, enum spi_sampling_mode);

/**
 * @brief   SPI 发送数据
 * 
 * @param   buffer                  - SPI发送数据buffer首地址
 * @param   length                  - SPI发送数据长度
 * 
 * @return  空
 */
void dev_spi_send_data(uint8_t *buffer, uint32_t length);

/**
 * @brief   SPI 接收数据
 * 
 * @param   buffer                  - SPI接收数据buffer首地址
 * @param   length                  - SPI接收数据长度
 * 
 * @return  空
 */
void dev_spi_recv_data(uint8_t *buffer, uint32_t length);

/**
 * @brief   SPI 发送数据后等待接收数据
 * 
 * @param   tx_buffer               - SPI发送数据buffer首地址
 * @param   n_tx                    - 发送数据长度
 * @param   rx_buffer               - SPI接收数据buffer首地址
 * @param   n_rx                    - 接收数据长度
 * 
 * @return  空
 */
void dev_spi_send_then_recv(uint8_t* tx_buffer, uint32_t n_tx, uint8_t* rx_buffer, uint32_t n_rx);
#endif /* __SPI_H__ */


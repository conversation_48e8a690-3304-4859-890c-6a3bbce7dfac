#ifndef __LED_H_
#define __LED_H_
#include "wlt_ble_type.h"

//电平
#define LED_LOW                 0x00
#define LED_HIGH                0x01

//LED1 LED2 输出
#define LED1_OUTPUT_HIGH dev_led1_write(LED_HIGH)
#define LED1_OUTPUT_LOW  dev_led1_write(LED_LOW)

#define LED2_OUTPUT_HIGH dev_led2_write(LED_HIGH)
#define LED2_OUTPUT_LOW  dev_led2_write(LED_LOW)

/**
 * @brief   LED1 输出电平配置
 * 
 * @param   value                   - 电平
 * 
 * @return  空
 */
void dev_led1_write(uint8_t value);

/**
 * @brief   LED2 输出电平配置
 * 
 * @param   value                   - 电平
 * 
 * @return  空
 */
void dev_led2_write(uint8_t value);
#endif /* __LED_H_ */

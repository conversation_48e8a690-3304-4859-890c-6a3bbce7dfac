#ifndef __WATCHDOG_H__
#define __WATCHDOG_H__

#include "sys_utils.h"

/**
 * @brief   看门狗初始化
 * 
 * @param   seconds                 - 看门狗定时时间
 * 
 * @return  空
 */
void dev_watchdog_init(uint8_t seconds);

/**
 * @brief   看门狗启动
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_watchdog_start(void);

/**
 * @brief   看门狗停止
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_watchdog_stop(void);

/**
 * @brief   喂狗
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_watchdog_feed(void);
#endif /* __WATCHDOG_H__ */


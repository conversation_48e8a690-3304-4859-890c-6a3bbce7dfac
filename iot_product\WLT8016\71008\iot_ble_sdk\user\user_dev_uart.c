#include "uart.h"
#include "gpio.h"
#include "user_dev.h"
#include "common.h"
#include "timer.h"

#define UART_ADDR UART0_ADDR
#define UART_BUFFER_SIZE (256)
#define UART_IDLE_INT   (0) //recived data 0: by frame 1: byte one by one

#if !UART_IDLE_INT
#define UART_TIMEOUT    (10000) //us
#define UART_USE_TIMER  TIMER_0
#endif


#define UART_RX_PULL_PIN    IO_PC6

static uint8_t buf[UART_BUFFER_SIZE] = {0};
static uint16_t buf_size = 0; //recived size

//---------------operations & callback---------------
#if UART_IDLE_INT
static void uart_cb(uint8_t *data, uint8_t length)
{
    mq_msg_t msg;
    if(init_msg(&msg,data,length) < 0){
        return;
    }
    send_mq(uart_dev.mq, &msg);
}
#else
static void timer_callback_func(void)
{
    mq_msg_t msg;
    //recive timeout, frame end.
    dev_timer_stop(UART_USE_TIMER);
    if(buf_size > 0){
        if(init_msg(&msg,buf,buf_size) < 0){
            return;
        }
        msg.from = MSG_FROM_UART;
        send_mq(&msg);
    }
    buf_size = 0;
    memset(buf,0,UART_BUFFER_SIZE);
}

static void uart_cb(uint8_t *data, uint8_t length)
{
    if(length != 1){
        IOT_LOG_W("data recived, fault length.");
        return;
    }

    buf[buf_size++] = *data;

    dev_timer_stop(UART_USE_TIMER);
    dev_timer_init(UART_USE_TIMER,UART_TIMEOUT);
    dev_timer_run(UART_USE_TIMER);

    if(buf_size >= UART_BUFFER_SIZE) {
        IOT_LOG_W("data recived, buf overflow.");
        buf_size = 0;
        memset(buf,0,UART_BUFFER_SIZE);
    }
}
#endif


static int dev_open(dev_t* dev)
{
    dev_uart_para_t para = {
        .dev_baudrate = BAUDRATE_115200,
        .dev_data_bit_num = DATA_BIT_NUM_8,
        .dev_parity = NO_PARITY,
        .dev_stop_bit = STOP_BIT_1,
    };
    if(dev == NULL){
        return -1;
    }
    if(!dev->has_init){
        dev_uart_init(UART_ADDR,IO_PA7,IO_PA6,para);
        dev_set_uart_callback(UART_ADDR, uart_cb);
#if !UART_IDLE_INT
        dev_timer_init(UART_USE_TIMER,UART_TIMEOUT);
        dev_set_timer_callback(UART_USE_TIMER,timer_callback_func);
#endif
    }
    //rx io上拉
    // dev_gpio_mode(UART_RX_PULL_PIN,PIN_MODE_OUTPUT);
    // dev_gpio_write(UART_RX_PULL_PIN,PIN_HIGH);

    dev->ref ++;
    return dev->ref;
}

static int dev_close(dev_t* dev)
{
    if(dev == NULL){
        return -1;
    }
    dev->ref --;
    if(dev->ref <= 0){
        dev->has_init = false;
        dev->ref = 0;
    }
    return dev->ref;
}

static int dev_read(dev_t* dev, uint8_t* data)
{
    if(dev == NULL){
        return -1;
    }
    return 0;
}

static int dev_write(dev_t* dev, uint8_t* data, int len)
{
    if(dev == NULL){
        return -1;
    }
    IOT_LOG_HEX_DUMP("send 2 mcu:",16,data,len);
    dev_uart_write(UART_ADDR, data, len);
    return 0;
}

static int dev_ioctl(dev_t* dev, int cmd, uint8_t *data)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    switch(cmd){
        default:
            IOT_LOG_I("invalid cmd.%d.",cmd);
            ret = -1;
            break;
    }
    return ret;
}

//--------- Real data structure
struct dev_ops uart_ops = {
    .open = dev_open,
    .close = dev_close,
    .read = dev_read,
    .write = dev_write,
    .ioctl = dev_ioctl
};

dev_t uart_dev = {
    .has_init = false,
    .ref = 0,
    .ops = &uart_ops
};


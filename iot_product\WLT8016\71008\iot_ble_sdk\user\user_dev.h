#ifndef __USER_DEV_H__
#define __USER_DEV_H__

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct dev_struct {
    bool has_init;
    int ref;
    uint16_t handle_excute; // process msg now, using this handle
    struct dev_ops* ops;
}dev_t;

struct dev_ops {
    int (*open)(dev_t* dev);
    int (*close)(dev_t* dev);
    int (*read)(dev_t* dev, uint8_t* data);
    int (*write)(dev_t* dev, uint8_t* data, int len);
    int (*ioctl)(dev_t* dev, int cmd, uint8_t *data);
};

typedef enum {
    //common
    DEV_IOCTL_CMD_CHECKDATA = 0,
    //ble
    DEV_IOCTL_BLE_CMD_DISCONNECT, //断开蓝牙连接
    DEV_IOCTL_BLE_CMD_ADV_ON, //开启蓝牙广播
    DEV_IOCTL_BLE_CMD_ADV_OFF, //关闭蓝牙广播
    DEV_IOCTL_BLE_CMD_ADV_BIND, //蓝牙广播数据为bind
    DEV_IOCTL_BLE_CMD_ADV_NORMAL, //蓝牙广播数据为normal
    DEV_IOCTL_BLE_CMD_ADV_HOST_ON, //蓝牙广播数据host段为主机开启
    DEV_IOCTL_BLE_CMD_ADV_HOST_OFF, //蓝牙广播数据host段为主机关闭
    DEV_IOCTL_BLE_CMD_ADV_REPORT_ON, //蓝牙广播数据report段为主机有数据
    DEV_IOCTL_BLE_CMD_ADV_REPORT_OFF, //蓝牙广播数据report段为主机无数据
    DEV_IOCTL_BLE_CMD_ADV_RTC_VALID, //蓝牙广播数据report段为RTC有效
    DEV_IOCTL_BLE_CMD_ADV_RTC_INVALID, //蓝牙广播数据report段为RTC无效
    DEV_IOCTL_BLE_CMD_DID_WRITE, //蓝牙广播数据device id改变
    DEV_IOCTL_BLE_CMD_GET_MAC, //获取蓝牙mac地址
    DEV_IOCTL_BLE_CMD_GET_CONN_STATUS, //获取蓝牙连接状态
    DEV_IOCTL_BLE_CMD_GET_BIND_STATUS, //获取蓝牙是否正在绑定状态
    DEV_IOCTL_BLE_CMD_SET_NORMAL_ADV_GAP, //配置正常模式广播间隔
    DEV_IOCTL_BLE_CMD_SET_SLEEP_ADV_GAP, //配置休眠模式广播间隔
    //flash
    DEV_IOCTL_FLASH_CMD_HAS_SFLASH, //获取当前板子是否有外部flash
    DEV_IOCTL_FLASH_CMD_INFO_R, //device info load
    DEV_IOCTL_FLASH_CMD_INFO_W, //device info store
    DEV_IOCTL_FLASH_CMD_PARAMETER_R, //device parameter load
    DEV_IOCTL_FLASH_CMD_PARAMETER_W, //device parameter store
    DEV_IOCTL_FLASH_CMD_TOTAL_R, //total data load
    DEV_IOCTL_FLASH_CMD_TOTAL_W, //total data store
    DEV_IOCTL_FLASH_CMD_TOTAL_CLEAR,//clear total data
    DEV_IOCTL_FLASH_CMD_GET_MCUOTA_ADDR_START, //获取MCU升级起始地址
    DEV_IOCTL_FLASH_CMD_ERASE_OTARESULT, //擦除存储升级结果的区域
    DEV_IOCTL_FLASH_CMD_READ_OTARESULT, //读取存储升级结果的区域
    DEV_IOCTL_FLASH_CMD_WRITE_OTARESULT, //写入存储升级结果的区域
    DEV_IOCTL_FLASH_CMD_ERASE_MCUOTA, //擦除存储MCU固件地址的区域
    DEV_IOCTL_FLASH_CMD_OTA_R, //OTA 数据读
    DEV_IOCTL_FLASH_CMD_OTA_W, //OTA 数据写
    DEV_IOCTL_FLASH_CMD_OTA_C, //OTA 数据擦除
    DEV_IOCTL_FLASH_CMD_OTA_GET_ADDR_START, //获取升级BLE自身固件起始地址
    DEV_IOCTL_FLASH_CMD_OTA_SELF_INIT, //升级BLE自身固件，初始化
    DEV_IOCTL_FLASH_CMD_OTA_SELF_W, //升级BLE自身固件，写入(无需考虑写入细节)
    DEV_IOCTL_FLASH_CMD_OTA_SELF_END, //升级BLE自身固件，结束
    DEV_IOCTL_FLASH_CMD_OTA_SELF_CRC, //升级获取的BLE自身固件CRC值
    DEV_IOCTL_FLASH_CMD_CHECK, //打印出数据
    DEV_IOCTL_FLASH_CMD_HISTORY_PUSH, //存入一条历史数据
    DEV_IOCTL_FLASH_CMD_HISTORY_GET_LAST, //获取最新的一条历史数据
    DEV_IOCTL_FLASH_CMD_HISTORY_GET_CONTINUE_START, //开始依次获取一条历史数据，直至最新
    DEV_IOCTL_FLASH_CMD_HISTORY_GET_CONTINUE, //依次获取一条历史数据，直至最新
    DEV_IOCTL_FLASH_CMD_HISTORY_DIAG_CONTINUE, //依次获取一条历史数据，用于诊断，直至最新
    DEV_IOCTL_FLASH_CMD_HISTORY_CLEAR, //清空历史数据，仅存留最新一条
    DEV_IOCTL_FLASH_CMD_HISTORY_HAS_DATA, //当前是否有数据 0为无
    DEV_IOCTL_FLASH_CMD_HISTORY_COUNT, //当前未读取的记录数量
    //system
    DEV_IOCTL_SYS_CMD_RESET, //重启
    DEV_IOCTL_SYS_CMD_WAKEMCU, //唤醒主机MCU
    DEV_IOCTL_SYS_CMD_LEDON, //亮LED灯
    DEV_IOCTL_SYS_CMD_LEDOFF, //灭LED灯
    DEV_IOCTL_SYS_CMD_WAKE_MCU_ON, //唤醒MCU的io引脚置高
    DEV_IOCTL_SYS_CMD_WAKE_MCU_OFF, //唤醒MCU的io引脚置低
    DEV_IOCTL_SYS_CMD_WAKEUPIO, //获取wakeup 端口电平状态
    DEV_IOCTL_SYS_CMD_WAKEUPIO_INSLEEP, //获取wakeup 端口电平状态,在sleep模式下
    DEV_IOCTL_SYS_CMD_HOST_STATUS, //获取host状态
    DEV_IOCTL_SYS_CMD_BAT_VOL, //获取纽扣电池电压
    DEV_IOCTL_SYS_CMD_INSLEEP, //进入sleep模式
    DEV_IOCTL_SYS_CMD_OUTSLEEP, //从sleep模式中恢复
    DEV_IOCTL_SYS_CMD_SN_R, //获取sn
    DEV_IOCTL_SYS_CMD_DID_R, //获取DID
    DEV_IOCTL_SYS_CMD_DID_W, //写入DID
    DEV_IOCTL_SYS_CMD_PID_R, //获取PID
    DEV_IOCTL_SYS_CMD_PID_W, //写入PID
    DEV_IOCTL_SYS_CMD_VER_M_R, //获取模组版本
    DEV_IOCTL_SYS_CMD_VER_M_W, //写入模组版本
    DEV_IOCTL_SYS_CMD_VER_D_R, //获取设备版本
    DEV_IOCTL_SYS_CMD_VER_D_W, //写入设备版本
    DEV_IOCTL_SYS_CMD_RTC_W, //rtc时间同步
    DEV_IOCTL_SYS_CMD_RTC_R, //rtc时间获取
    DEV_IOCTL_SYS_CMD_RTC_R_STAMP, //时间获取(时间戳格式,单位秒)
    DEV_IOCTL_SYS_CMD_RTC_W_STAMP, //保存系统时间戳,单位秒)
    DEV_IOCTL_SYS_CMD_SLEEP_ADV_GAP_W, //休眠广播间隔写入
    DEV_IOCTL_SYS_CMD_SLEEP_ADV_GAP_R, //休眠广播间隔读出
    DEV_IOCTL_SYS_CMD_GET_IS_SLEEP, //获取当前是否处于休眠模式
    DEV_IOCTL_SYS_CMD_APP_IDENTIFY_W,
    DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R,

    DEV_IOCTL_SYS_CMD_TOTAL_R, //获取 total info 
    DEV_IOCTL_SYS_CMD_TOTAL_W, //set total info
    DEV_IOCTL_SYS_CMD_TOTAL_CLEAR, //clear total info

} DEV_IOCTL_CMD;

typedef struct  {
    uint32_t addr;
    uint8_t * data;
    uint32_t len;
    uint8_t number;
    uint32_t data32_rsv;
    uint32_t *ret_len;
} dev_data_flash_t;

//------Declaration of device--------
extern dev_t uart_dev;
extern dev_t bt_dev;
extern dev_t flash_dev;
extern dev_t sys_dev;

#ifdef __cplusplus
}
#endif

#endif /* __USER_DEV_H__ */

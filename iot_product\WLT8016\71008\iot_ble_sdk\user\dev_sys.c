#include "wlt_util.h"
#include "system.h"
#include "user_vfs.h"
#include "gpio.h"
#include "exti.h"
#include "sleep.h"
#include "jump_table.h"
#include "rtc.h"
#include "time.h"
#include "limits.h"
#include "user_config.h"
#include "ble_gap.h"
#include "timer.h"
#include "adc.h"
#include "watchdog.h"


#define TEST_VALUE  0

#define WATCH_DOG_CYCLE (60)      //看门狗超时时间

#define LED_PIN         (IO_PC5)  //模组控制指示灯端口
#define LED_PIN_MODE    (PIN_MODE_OUTPUT)
#define WAKEUP_PIN      (IO_PA1)  //用于唤醒模组的IO口
#define WAKEUP_PIN_MODE (PIN_MODE_INPUT)
#define WAKEUP_MCU_PIN         (IO_PD5)  //模组唤醒MCU的IO口
#define WAKEUP_MCU_PIN_MODE    (PIN_MODE_OUTPUT)
#define SLEEP_GAP_MAX_MS        (1000) //1s
#define SLEEP_GAP_MIN_MS        (200) //200ms
#define USER_INACTIVE_VALUE     (0x9999) //用于识别用户是否未激活，可进入出厂模式
#define WRITE_DID_FLAG_VALID    (0x88888888) //当前上电是否写入DID的有效值
#define WRITE_DID_OFFSET_USER_INACTIVE  (24*60*60*1000) //24H 时间到则置位出厂模式的偏移

static uint8_t is_sleep = 0;//休眠模式中
// static uint32_t write_did_flag = 0; //当前上电是否写入DID。有效值为WRITE_DID_FLAG_VALID
// static uint32_t write_did_systime = 0; //需要置位出厂模式的时间，当前上电写入DID的系统时间+WRITE_DID_OFFSET_USER_INACTIVE，单位ms.

#define FLASH_PROTECT_VOLTAGE_2_4V  0x01
#define FLASH_PROTECT_VOLTAGE_2_0V  0x02

#define FLASH_PROTECT_VOLTAGE       FLASH_PROTECT_VOLTAGE_2_4V

#if (FLASH_PROTECT_VOLTAGE == FLASH_PROTECT_VOLTAGE_2_4V)
#define FLASH_PROTECT_RETURN_VALUE  0
#endif

#if (FLASH_PROTECT_VOLTAGE == FLASH_PROTECT_VOLTAGE_2_0V)
#define FLASH_PROTECT_RETURN_VALUE  1
#endif



uint8_t user_flash_protect_voltage(void)
{
    return FLASH_PROTECT_RETURN_VALUE;
}

/*  brief
*   log system switch, for wlt sdk
*/
uint8_t user_log_system_switch(void)
{
    return LOG_SYSTEM_SWITCH;
}

#define IMAGE_TYPE  (0x33333333)    //固定值，不需要修改
#define IMAGE_SIZE  (0x25800) //固件大小设置为152KB,这个值需要为4KB的整数倍,并且这个值必须小于等于240KB

const struct jump_table_image_t _jump_table_image __attribute__((section("jump_table_1"))) =
{
    .image_type = IMAGE_TYPE,
    .image_size = IMAGE_SIZE,
};

/*  brief
*   define image size of A&B, for wlt sdk
*/
uint32_t user_image_size(void)
{
    //the max image size set to 0x25800(150KB),this value mast <= 0x3C000(240KB)
    return 0x25800;
}

void user_into_lvd_callback(void)
{
    IOT_LOG_I("user_into_lvd_callback");
}

void user_exit_lvd_callback(void)
{
    IOT_LOG_I("user_exit_lvd_callback");
}

void user_charge_callback(unsigned char reason)
{
    switch (reason)
    {
    case 0:
        IOT_LOG_I("charge start");
        break;
    case 1:
        IOT_LOG_I("charge stop");
        break;
    case 2:
        IOT_LOG_I("charge full");
        break;
    default:
        break;
    } 
}

/**********************************************************************
* Interface of LED
***********************************************************************/
static int sys_led_init(void)
{
    dev_gpio_mode(LED_PIN,LED_PIN_MODE);
    return 0;
}

static int sys_led_on(void)
{
    dev_gpio_write(LED_PIN,1);
    return 0;
}

static int sys_led_off(void)
{
    dev_gpio_write(LED_PIN,0);
    return 0;
}

/**********************************************************************
* Interface of WAKEUP MCU
***********************************************************************/
static int sys_wakeup_mcu_init(void)
{
    dev_wakeupgpio_reset(WAKEUP_MCU_PIN);
    dev_gpio_mode(WAKEUP_MCU_PIN,WAKEUP_MCU_PIN_MODE);
    return 0;
}

static int sys_wakeup_mcu_on(void)
{
    dev_gpio_write(WAKEUP_MCU_PIN,1);
    return 0;
}

static int sys_wakeup_mcu_off(void)
{
    dev_gpio_write(WAKEUP_MCU_PIN,0);
    return 0;
}

/**********************************************************************
* Interface of WAKEUP IO
***********************************************************************/
static int sys_wakeup_io_init(void)
{
    //作为普通IO口使用时的初始化
    dev_wakeupgpio_reset(WAKEUP_PIN);
    dev_gpio_mode(WAKEUP_PIN,WAKEUP_PIN_MODE);
    return 0;
}

/* brief:
*  获取当前管脚电平状态。0为低电平，1为高电平
*/
static int sys_wakeup_io_read(void)
{
    //PIN_HIGH 为1，PIN_LOW为0
    return (dev_gpio_read(WAKEUP_PIN) == PIN_HIGH)? 1:0;
}

/* brief: 在休眠时检测使用
*  获取当前管脚电平状态。0为低电平，1为高电平
*/
static int sys_wakeup_io_insleep_read(void)
{
    //PIN_HIGH 为1，PIN_LOW为0
    return (dev_wakeupgpio_get(WAKEUP_PIN) == PIN_HIGH)? 1:0;
}

static int get_host_status(void)
{
    if(is_sleep == 1)
    {
        return (dev_wakeupgpio_get(WAKEUP_PIN) == PIN_HIGH)? 1:0;
    }
    else
    {
        return (dev_gpio_read(WAKEUP_PIN) == PIN_HIGH)? 1:0;
    }
}

/**********************************************************************
* Interface of adc
***********************************************************************/
static uint8_t sys_get_bat_vol(void)
{
    uint16_t Value_H,Value_L;
    uint16_t voltage_value = 0;
    uint8_t voltage_bat = 0;

    dev_adc_init(ADC_CHANNEL_0, ADC_REFERENCE_CFG_INTERNAL);
    dev_adc_enable();
    dev_adc_get_result(ADC_CHANNEL_0, &voltage_value);
    voltage_value=((voltage_value*12)*4680)/680;
    Value_H=voltage_value/1000;
    Value_L=voltage_value%1000;
    //IOT_LOG_I("%d %d %d\r\n",voltage_bat,Value_H,Value_L);
    if(Value_L>500)Value_H+=1;
    if(Value_H>42)Value_H=42;
    voltage_bat=Value_H;
    dev_adc_disable();
    log_i("ADC_CHANNEL_0 :%d bat_vol=%d\r\n",voltage_value,voltage_bat);
    return voltage_bat;
}

/**********************************************************************
* Interface of device parameter
***********************************************************************/
device_para_t device_para_data;//设备配置信息缓存
/* brief:
*  store 相关信息到flash，如休眠广播间隔，需要调用
*/
static int sys_store_parameter(void)
{
    IOT_LOG_I("sys_store_parameter.");
    dev_data_flash_t tData;
    tData.data = (uint8_t *)(&device_para_data);
    tData.len = sizeof(device_para_data);
    return ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_PARAMETER_W,(uint8_t*)(&tData));
}

/* brief:
*  系统在初始状态，load相关信息到RAM，如休眠广播间隔
*/
static int sys_load_parameter(void)
{
    IOT_LOG_I("sys_load_parameter.");
    int ret = 0;
    dev_data_flash_t tData;
    tData.data = (uint8_t *)(&device_para_data);
    tData.len = sizeof(device_para_t);
    ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_PARAMETER_R,(uint8_t*)(&tData));
    log_i("load param: rtcstamp:%d, gap:%d, app:%s,",device_para_data.rtc_timestamp,device_para_data.adv_interval,
        (device_para_data.identify_is_fleet==1)?"fleet":"connect");
    return ret;
}

/* brief:
*  获取休眠广播间隔信息
*/
static int sys_get_sleep_adv_gap(uint16_t *data)
{
    if(device_para_data.adv_interval > SLEEP_GAP_MAX_MS){
        *data = SLEEP_GAP_MAX_MS;
    }else if(device_para_data.adv_interval < SLEEP_GAP_MIN_MS){
        *data = SLEEP_GAP_MIN_MS;
    }else{
        *data = device_para_data.adv_interval;
    }
    return 0;
}

/* brief:
*  保存休眠广播间隔信息，不改变则不写入
*/
static int sys_set_sleep_adv_gap(uint16_t *data)
{
    int ret = 0;
    uint16_t adv = 0;
    sys_get_sleep_adv_gap(&adv);
    if(*data != adv){
        device_para_data.adv_interval = *data;
        ret = sys_store_parameter();
    }
    return ret;
}

/* brief:
*  获取app 认证信息
*/
static int sys_get_app_identify(uint8_t *data)
{
    if(device_para_data.identify_is_fleet == 1){
        *data = 1;
    }
    else
    {
        *data =0;
    }
    return 0;
}
/* brief:
*  保存app 认证信息
*/
static int sys_set_app_identify(uint8_t *data)
{
    uint8_t identify=0;
    sys_get_app_identify(&identify);
    if(identify != *data)
    {
        device_para_data.identify_is_fleet = *data;
        sys_store_parameter();
    }
    return 0;
}



/**********************************************************************
* Interface of DID PID VERSION 
***********************************************************************/
device_info_t device_info_data;//设备信息缓存
/* brief:
*  store 相关信息到flash，如DID，SN，PID，version，需要调用
*/
static int sys_store_info(void)
{
    IOT_LOG_I("enter.");
    dev_data_flash_t tData;
    tData.data = (uint8_t *)(&device_info_data);
    tData.len = sizeof(device_info_t);
    return ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_INFO_W,(uint8_t*)(&tData));
}

/* brief:
*  系统在初始状态，load相关信息到RAM，如DID，SN，PID，version
*/
static int sys_load_info(void)
{
    IOT_LOG_I("enter.");
    int ret = 0;
#if TEST_VALUE
#if 0
    //11012 LF2240T
    uint8_t did[LEN_DID] = {0x58,0x42,0x41,0x32,0x30,0x32,0x32,0x39,
                     0x30,0x36,0x37,0x36,0x39,0x38,0x30};
    uint8_t sn[LEN_SN] = {0x42,0x41,0x32,0x30};
#else
    //12010 CHX1400
    uint8_t did[LEN_DID] = {0x58,0x43,0x48,0x31,0x36,0x32,0x32,0x39,
                     0x30,0x36,0x37,0x36,0x39,0x37,0x35};
    uint8_t sn[LEN_SN] = {0x43,0x48,0x31,0x36};
#endif
    memcpy(device_info_data.did,did,LEN_DID);
    memcpy(device_info_data.sn,sn,LEN_SN);
    device_info_data.did_valid = VALID_CODE;
    sys_store_info();
    return 0;
#endif
    dev_data_flash_t tData;
    tData.data = (uint8_t *)(&device_info_data);
    tData.len = sizeof(device_info_t);
    ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_INFO_R,(uint8_t*)(&tData));
    IOT_LOG_HEX_DUMP("DID:",16,device_info_data.did,LEN_DID);
    IOT_LOG_D("DID is %s.",(device_info_data.did_valid == VALID_CODE)?"valid":"invalid");
    IOT_LOG_HEX_DUMP("SN:",16,device_info_data.sn,LEN_SN);
    return ret;
}

/* brief:
*  获取SN信息
*/
static int sys_get_sn(uint8_t *data)
{
    if(device_info_data.did_valid == VALID_CODE){
        memcpy(data,device_info_data.sn,LEN_SN);
        IOT_LOG_D("get sn success.");
        return 0;
    }
    return -1;
}

/* brief:
*  获取DID信息
*/
static int sys_get_did(uint8_t *data)
{
    IOT_LOG_D("get did. valid = %d.",device_info_data.did_valid);
    if(device_info_data.did_valid == VALID_CODE){
        memcpy(data,device_info_data.did,LEN_DID);
        IOT_LOG_I("get did success.");
        IOT_LOG_HEX_DUMP("did",16,device_info_data.did,LEN_DID);
        return 0;
    }
    return -1;
}

/* brief:
*  保存DID信息，不改变则不写入
*/
static int sys_store_did(uint8_t *data)
{
    int ret = 0;
    uint8_t rdid[LEN_DID] = {0};
    sys_get_did(rdid);
    if(strncmp((char *)&(rdid[0]),(char *)data,LEN_DID) != 0){
        memcpy(device_info_data.did,data,LEN_DID);
        memcpy(device_info_data.sn,&(data[1]),LEN_SN);//sotre sn offset 1
        device_info_data.did_valid = VALID_CODE;
        ret = sys_store_info();
        product_init();
    }
    return ret;
}

/* brief:
*  获取PID信息
*/
static int sys_get_pid(uint8_t *data)
{
    if(device_info_data.pid_valid == VALID_CODE){
        memcpy(data,device_info_data.pid,LEN_PID);
        IOT_LOG_I("success.");
//        IOT_LOG_HEX_DUMP("pid",8,device_info_data.did,LEN_DID);
        return 0;
    }
    return -1;
}

/* brief:
*  保存PID信息，不改变则不写入
*/
static int sys_store_pid(uint8_t *data)
{
    int ret = 0;
    uint8_t rpid[LEN_PID] = {0};
    sys_get_pid(rpid);
    if(strncmp((char *)&(rpid[0]),(char *)data,LEN_PID) != 0){
        memcpy(device_info_data.pid,data,LEN_PID);
        device_info_data.pid_valid = VALID_CODE;
        ret = sys_store_info();
    }
    return ret;
}

/* brief:
*  获取version module信息
*/
static int sys_get_ver_module(uint8_t *data)
{
    if(device_info_data.ver_m_valid == VALID_CODE){
        memcpy(data,device_info_data.ver_module,LEN_VER_M);
        IOT_LOG_I("success.");
//        IOT_LOG_HEX_DUMP("module ver",8,device_info_data.ver_module,LEN_VER_M);
    }else{
        memcpy(data,IOT_MODULE_VER_HARD,IOT_MODULE_VER_HARD_REAL_LEN);
        memcpy(data+(LEN_VER_M/2),IOT_MODULE_VER_SOFT,IOT_MODULE_VER_SOFT_REAL_LEN);
    }
    return 0;
}

/* brief:
*  保存version module信息，不改变则不写入
*/
static int sys_store_ver_module(uint8_t *data)
{
    int ret = 0;
    uint8_t rver[LEN_VER_M] = {0};
    sys_get_ver_module(rver);
    if(strncmp((char *)&(rver[0]),(char *)data,LEN_VER_M) != 0){
        memcpy(device_info_data.ver_module,data,LEN_VER_M);
        device_info_data.ver_m_valid = VALID_CODE;
        ret = sys_store_info();
    }
    return ret;
}

/* brief:
*  获取version device信息
*/
static int sys_get_ver_device(uint8_t *data)
{
    if(device_info_data.ver_d_valid == VALID_CODE){
        memcpy(data,device_info_data.ver_device,LEN_VER_D);
        IOT_LOG_I("success.");
//        IOT_LOG_HEX_DUMP("module ver",8,device_info_data.ver_module,LEN_VER_M);
        return 0;
    }
    return -1;
}

/* brief:
*  保存version device信息，不改变则不写入
*/
static int sys_store_ver_device(uint8_t *data)
{
    int ret = 0;
    uint8_t rver[LEN_VER_D] = {0};
    sys_get_ver_device(rver);
    if(strncmp((char *)&(rver[0]),(char *)data,LEN_VER_D) != 0){
        memcpy(device_info_data.ver_device,data,LEN_VER_D);
        device_info_data.ver_d_valid = VALID_CODE;
        ret = sys_store_info();
    }
    return ret;
}


/**********************************************************************
* Interface of TOTAL_DATA_INFO  设备统计total类数据
***********************************************************************/

total_info_t  dev_total_data;

/* brief:
*  保存更新total数据，不改变则不写入
*/
static int sys_store_total_data(total_info_t *data)
{
    int ret = 0;
    // log_i("storr:%d,%d,%d,%d....",data->valid_code,data->Last_worktime,data->total_worktime,data->total_power_consumption);
    
    if(memcmp(&dev_total_data,data,sizeof(total_info_t)) != 0)
    {
        memcpy(&dev_total_data,data,sizeof(total_info_t));
        // log_i("store:%d,%d,%d,%d....",dev_total_data.valid_code,dev_total_data.Last_worktime,dev_total_data.total_worktime,dev_total_data.total_power_consumption);

        dev_data_flash_t tData;
        tData.data = (uint8_t *)(&dev_total_data);
        tData.len = sizeof(total_info_t);
        ret=ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_TOTAL_W,(uint8_t*)(&tData));
    }
    return ret;
}

/* brief:
*  系统在初始状态，load相关信息到RAM
*/
static int sys_load_total_data(void)
{
    IOT_LOG_I("enter.");
    int ret = 0;

    dev_data_flash_t tData;
    tData.data = (uint8_t *)(&dev_total_data);
    tData.len = sizeof(total_info_t);
    ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_TOTAL_R,(uint8_t*)(&tData));

    if(dev_total_data.valid_code != VALID_CODE)//未存储过total,初始化为0
    {
        memset(&dev_total_data,0,sizeof(dev_total_data));
        dev_total_data.valid_code = VALID_CODE;
    }
    log_i("load total:code:%d, 1027:%d, 1016:%d, 1025:%d.",dev_total_data.valid_code,dev_total_data.Last_worktime,dev_total_data.total_worktime,dev_total_data.total_power_consumption);
    return ret;
}

static int sys_get_total_data(total_info_t *data)
{
    memcpy(data,&dev_total_data,sizeof(total_info_t));
    return 0;
}

static int sys_set_total_data(total_info_t *data)
{
    int ret = 0;
    data->valid_code = VALID_CODE;
    ret = sys_store_total_data(data);
    return ret;
}

/* brief:
*  系统在初始状态，load相关信息到RAM
*/
static int sys_clear_total_data(void)
{
    int ret = 0;
    memset(&dev_total_data,0,sizeof(dev_total_data));
    ret=ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_TOTAL_CLEAR,NULL);
    return ret;
}





/**********************************************************************
* Interface of RTC
***********************************************************************/
extern rtc_clock_t clock_env;

/**
 * 设置系统RTC时间
 */
static void dev_rtc_set_time(rtc_clock_t *sys_rtc)
{
    memset(&clock_env,0,sizeof(rtc_clock_t));
	memcpy(&clock_env,sys_rtc,sizeof(rtc_clock_t));
}

/********************************************************************
 * 功能描述：日期转换成时间戳
 * 
 * 参数说明：date [i] - 日期时间
 *
 * 返 回 值：Unix秒级时间戳，失败返回0
 *
 * 其他说明：无
********************************************************************/
static uint32_t rtc_to_timestamp(rtc_clock_t *sys_rtc)
{
    if(sys_rtc == NULL) 
    {
        return 0;
    }
	struct tm time={0};

    time.tm_year = sys_rtc->year-1900;
	time.tm_mon = sys_rtc->month-1;
	time.tm_mday = sys_rtc->day;
	time.tm_hour = sys_rtc->hour;
	time.tm_min = sys_rtc->min;
	time.tm_sec = sys_rtc->sec;

    return mktime(&time);
}

/********************************************************************
 * 功能描述：时间戳转成日期
 * 
 * 参数说明：ts   [i] - Unix秒级时间戳
 *          date [o] - 日期时间
 *
 * 返 回 值：0 - 成功， -1 - 失败
 *
 * 其他说明：无
********************************************************************/
static int timestamp_to_rtc(uint32_t unix ,rtc_clock_t *sys_rtc)
{
    if(sys_rtc == NULL) 
    {
        return -1;
    }
    struct tm *time=NULL;
    time=localtime((time_t *)&unix);
    if(time == NULL)
    {
        return -1;
    }

    sys_rtc->year=time->tm_year+1900;
    sys_rtc->month=time->tm_mon+1;
    sys_rtc->day=time->tm_mday;
    sys_rtc->hour=time->tm_hour;
    sys_rtc->min=time->tm_min;
    sys_rtc->sec=time->tm_sec;
    sys_rtc->week = time->tm_wday;

    return 0;
}

static void printtime(void)
{
    rtc_clock_t time={0};
    uint32_t stamp = 0;

    dev_rtc_get_time(&time);
    stamp=rtc_to_timestamp(&time);

    log_i("sys time:%d/%d/%d %d:%d:%d; %d.",time.year,
    time.month,time.day,time.hour,time.min,time.sec,stamp);
}


/**
 * 获取系统RTC时间 -   出参:时间戳格式
 */
static int dev_rtc_get_timestamp(uint32_t *unix)
{
    if(unix == NULL)
    {
        return -1;
    }

    rtc_clock_t sys_rtc={0};

	dev_rtc_get_time(&sys_rtc);
    *unix = rtc_to_timestamp(&sys_rtc);

    return 0;
}


#define SYS_RTC_VALID_TIMESTAMP     (1704067200)//20240101 00:00:00

static int sys_rtc_timestamp_save(void)
{
    uint32_t timestamp=0;
    dev_rtc_get_timestamp(&timestamp);
    if(timestamp > SYS_RTC_VALID_TIMESTAMP && timestamp!=0xffffffff)
    {
        device_para_data.rtc_timestamp = timestamp;
        sys_store_parameter();
        return 0;
    }
    return -1;
}

static void rtc_default_configuration(void)
{
    uint8_t is_sleep=0;
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_GET_IS_SLEEP,&is_sleep);
    if(is_sleep == 0)//从休眠状态唤醒不需要重新初始化rtc
    {
        rtc_clock_t clock={0};
        if(device_para_data.rtc_timestamp > SYS_RTC_VALID_TIMESTAMP && device_para_data.rtc_timestamp!=0xffffffff)
        {
            timestamp_to_rtc(device_para_data.rtc_timestamp,&clock);
        }
        else
        {
            clock.year = 2000;//默认2020-01-01 00:00:00
            clock.month = 1;
            clock.day = 1;
            clock.week = 6;
            clock.hour = 0;
            clock.min = 0;
            clock.sec = 0;
        }
        dev_rtc_set_time(&clock);
    }
}

static int sys_rtc_init(void)
{
    rtc_default_configuration();
    dev_rtc_start(GET_RTC_INTERVAL); //设定RTC时间更新间隔，开始从2023.1.1开始计时
    printtime();
    return 0;
}

static int sys_rtc_set_time(rtc_clock_t * data)
{
    if(data == NULL){
        return -1;
    }
    dev_rtc_set_time(data);
    printtime();
    return 0;
}

static int sys_rtc_get_time(rtc_clock_t * data)
{
    if(data == NULL){
        return -1;
    }
    dev_rtc_get_time(data);
    printtime();
    return 0;
}


/**********************************************************************
* Interface of system
***********************************************************************/
static int sys_reset(void)
{
    sys_rtc_timestamp_save();
    wlt_reset();
    return 0;
}
static int sys_in_sleep(void)
{
    is_sleep = 1;
    dev_timer_stop(TIMER_0);
    dev_timer_stop(TIMER_1);
    dev_wakeup_io_init(WAKEUP_PIN, WAKEUPIO_NOT_PULL_UP); //PA1作为唤醒源 IO口不上拉，高电平有效
    dev_gpio_write(WAKEUP_MCU_PIN, PIN_LOW); //配置进入休眠后唤醒MCU的端口为低电平
    wlt_clear_user_loop_callback(); //清除运行在while(1)中的loop事件 如果不清除 无法进入sleep模式
    dev_rtc_start(GET_RTC_INTERVAL_SLEEP);//rtc 每20s唤醒调用user_wakeup_callback
    dev_into_sleep(); //进入sleep模式 唤醒程序不重启
    return 0;
}

static int sys_out_sleep(void)
{
    is_sleep = 0;
    return 0;
}


/**********************************************************************
* Interface of system init 
***********************************************************************/
static void sys_dev_init(void)
{
    sys_load_info();//设备信息初始化
    sys_load_parameter();//设备参数初始化
    sys_load_total_data();//total类数据初始化

    sys_rtc_init();//RTC 初始化
    sys_led_init();//led 初始化
    sys_wakeup_mcu_init();//wakeup mcu io 初始化
    sys_wakeup_io_init();//唤醒/检测主机脚初始化

    dev_watchdog_init(WATCH_DOG_CYCLE);
    dev_watchdog_start();//启动看门狗
    
}

/**********************************************************************
* operations & callback
***********************************************************************/
static int dev_open(dev_t* dev)
{
    if(dev == NULL){
        return -1;
    }
    if(!dev->has_init){
        dev->has_init = true;
        sys_dev_init();
    }
    dev->ref ++;
    return dev->ref;
}

static int dev_close(dev_t* dev)
{
    if(dev == NULL){
        return -1;
    }
    dev->ref --;
    if(dev->ref <= 0){
        dev->has_init = false;
        dev->ref = 0;
    }
    return dev->ref;
}

static int dev_read(dev_t* dev, uint8_t* data)
{
    int ret = 0;
    if(dev == NULL || data == NULL){
        return -1;
    }
    return ret;
}

static int dev_write(dev_t* dev, uint8_t* data, int len)
{
    int ret = 0;
    if(dev == NULL || data == NULL || len < 0){
        return -1;
    }
    return ret;
}

static int dev_ioctl(dev_t* dev, int cmd, uint8_t *data)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    switch(cmd){
        case DEV_IOCTL_SYS_CMD_RESET:
            ret = sys_reset();
            break;
        case DEV_IOCTL_SYS_CMD_LEDON:
            ret = sys_led_on();
            break;
        case DEV_IOCTL_SYS_CMD_LEDOFF:
            ret = sys_led_off();
            break;
        case DEV_IOCTL_SYS_CMD_WAKE_MCU_ON:
            ret = sys_wakeup_mcu_on();
            break;
        case DEV_IOCTL_SYS_CMD_WAKE_MCU_OFF:
            ret = sys_wakeup_mcu_off();
            break;
        case DEV_IOCTL_SYS_CMD_WAKEUPIO:
            if(data == NULL){
                ret = -1;
                break;
            }
            *data = sys_wakeup_io_read();
            break;
        case DEV_IOCTL_SYS_CMD_WAKEUPIO_INSLEEP:
            if(data == NULL){
                ret = -1;
                break;
            }
            *data = sys_wakeup_io_insleep_read();
            break;
        case DEV_IOCTL_SYS_CMD_HOST_STATUS:
            if(data == NULL){
                ret = -1;
                break;
            }
            *data = get_host_status();
            break;
        case DEV_IOCTL_SYS_CMD_BAT_VOL:
            if(data == NULL){
                ret = -1;
                break;
            }
            *data = sys_get_bat_vol();
            break;


        //sleep    
        case DEV_IOCTL_SYS_CMD_INSLEEP:
            ret = sys_in_sleep();
            break;
        case DEV_IOCTL_SYS_CMD_OUTSLEEP:
            ret = sys_out_sleep();
            break;
        
        case DEV_IOCTL_SYS_CMD_GET_IS_SLEEP:
            *data = is_sleep;
        break;
        
        //dev info
        case DEV_IOCTL_SYS_CMD_SN_R:
            ret = sys_get_sn(data);
            break;
        case DEV_IOCTL_SYS_CMD_DID_R:
            ret = sys_get_did(data);
            break;
        case DEV_IOCTL_SYS_CMD_DID_W:
            ret = sys_store_did(data);
            break;
        case DEV_IOCTL_SYS_CMD_PID_R:
            ret = sys_get_pid(data);
            break;
        case DEV_IOCTL_SYS_CMD_PID_W:
            ret = sys_store_pid(data);
            break;
        case DEV_IOCTL_SYS_CMD_VER_M_R:
            ret = sys_get_ver_module(data);
            break;
        case DEV_IOCTL_SYS_CMD_VER_M_W:
            ret = sys_store_ver_module(data);
            break;
        case DEV_IOCTL_SYS_CMD_VER_D_R:
            ret = sys_get_ver_device(data);
            break;
        case DEV_IOCTL_SYS_CMD_VER_D_W:
            ret = sys_store_ver_device(data);
            break;

        //dev parame    
        case DEV_IOCTL_SYS_CMD_RTC_W:
            ret = sys_rtc_set_time((rtc_clock_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_RTC_R:
            ret = sys_rtc_get_time((rtc_clock_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_RTC_R_STAMP:
            ret = dev_rtc_get_timestamp((uint32_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_RTC_W_STAMP:
            ret = sys_rtc_timestamp_save();
            break;
        case DEV_IOCTL_SYS_CMD_SLEEP_ADV_GAP_W:
            ret = sys_set_sleep_adv_gap((uint16_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_SLEEP_ADV_GAP_R:
            ret = sys_get_sleep_adv_gap((uint16_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_APP_IDENTIFY_W:
            ret = sys_set_app_identify((uint8_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R:
            ret = sys_get_app_identify((uint8_t*)data);
            break;

        //dev total    
        case DEV_IOCTL_SYS_CMD_TOTAL_R:
            ret = sys_get_total_data((total_info_t*)data);
            break; 
        case DEV_IOCTL_SYS_CMD_TOTAL_W:
            ret = sys_set_total_data((total_info_t*)data);
            break;
        case DEV_IOCTL_SYS_CMD_TOTAL_CLEAR:
            ret = sys_clear_total_data();
            break;

        default:
            IOT_LOG_I("invalid cmd.%d.",cmd);
            ret = -1;
            break;
    }
    return ret;
}

//--------- Real data structure
struct dev_ops sys_ops = {
    .open = dev_open,
    .close = dev_close,
    .read = dev_read,
    .write = dev_write,
    .ioctl = dev_ioctl
};

dev_t sys_dev = {
    .has_init = false,
    .ref = 0,
    .ops = &sys_ops
};



<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>ble_simple_peripheral</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM3</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.9.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M3") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM3$Device\ARM\ARMCM3\Include\ARMCM3.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>wlt8828_ble_firmware</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>fromelf.exe  --text  -c -o Output/wlt8828_ble_firmware.txt  Objects/wlt8828_ble_firmware.axf</UserProg1Name>
            <UserProg2Name>fromelf.exe  --bin -o Output/wlt8828_ble_firmware.bin  Objects/wlt8828_ble_firmware.axf</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4100</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>Segger\JL2CM3.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M3"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USER_MEM_API_ENABLE</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\..\components\ble\include;..\..\..\..\components\driver\include;..\..\..\..\components\modules\os\include;..\..\..\..\components\modules\sys\include;..\..\..\..\components\modules\platform\include;..\..\..\..\components\modules\common\include;..\..\..\..\components\modules\lowpow\include;..\..\..\..\components\modules\button;..\..\..\..\components\ble\include\gap;..\..\..\..\components\ble\include\gatt;..\..\..\..\components\ble\profiles\ble_simple_profile;..\code;..\..\..\..\wlt\device;..\..\..\..\wlt\bsp;..\..\..\..\wlt;..\..\..\..\iot_ble_sdk\wlt_ble;..\..\..\..\iot_ble_sdk\components\wltkrnl;..\..\..\..\iot_ble_sdk\include;..\..\..\..\iot_ble_sdk\components\easylogger\inc;..\..\..\..\iot_ble_sdk\board\include;..\..\..\..\iot_ble_sdk\components\util;..\..\..\..\iot_ble_sdk\user</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\ble_5_0.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--feedback=feedback.txt ../../../../components/ble/library/syscall.txt --entry=app_main --keep=_jump_table_reserved --keep=_jump_table_version --keep=_jump_table_image --keep=_jump_table_middle --keep=_jump_table_last --datacompressor=off</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>platform</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <uClangAs>2</uClangAs>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>app_boot_vectors.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\..\components\modules\platform\source\app_boot_vectors.s</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Aads>
                    <interw>2</interw>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <thumb>2</thumb>
                    <SplitLS>2</SplitLS>
                    <SwStkChk>2</SwStkChk>
                    <NoWarn>2</NoWarn>
                    <uSurpInc>2</uSurpInc>
                    <useXO>2</useXO>
                    <uClangAs>2</uClangAs>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Aads>
                </FileArmAds>
              </FileOption>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component</GroupName>
          <Files>
            <File>
              <FileName>wlt_krnl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\wltkrnl\wlt_krnl.c</FilePath>
            </File>
            <File>
              <FileName>wlt_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\util\wlt_util.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component/easylogger</GroupName>
          <Files>
            <File>
              <FileName>elog_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\src\elog_utils.c</FilePath>
            </File>
            <File>
              <FileName>elog_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\src\elog_buf.c</FilePath>
            </File>
            <File>
              <FileName>elog_async.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\src\elog_async.c</FilePath>
            </File>
            <File>
              <FileName>elog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\src\elog.c</FilePath>
            </File>
            <File>
              <FileName>elog_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\port\elog_port.c</FilePath>
            </File>
            <File>
              <FileName>elog.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\inc\elog.h</FilePath>
            </File>
            <File>
              <FileName>elog_cfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\components\easylogger\inc\elog_cfg.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <Files>
            <File>
              <FileName>user_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_main.c</FilePath>
            </File>
            <File>
              <FileName>service.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\service.c</FilePath>
            </File>
            <File>
              <FileName>service.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\service.h</FilePath>
            </File>
            <File>
              <FileName>module.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module.c</FilePath>
            </File>
            <File>
              <FileName>module.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module.h</FilePath>
            </File>
            <File>
              <FileName>module_ledbtn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module_ledbtn.c</FilePath>
            </File>
            <File>
              <FileName>module_normal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module_normal.c</FilePath>
            </File>
            <File>
              <FileName>module_model.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module_model.c</FilePath>
            </File>
            <File>
              <FileName>module_mode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module_mode.c</FilePath>
            </File>
            <File>
              <FileName>module_ota.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module_ota.c</FilePath>
            </File>
            <File>
              <FileName>module_iap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\module_iap.c</FilePath>
            </File>
            <File>
              <FileName>protocol.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\protocol.h</FilePath>
            </File>
            <File>
              <FileName>protocol_V0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\protocol_V0.c</FilePath>
            </File>
            <File>
              <FileName>protocol_Vnegative.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\protocol_Vnegative.c</FilePath>
            </File>
            <File>
              <FileName>protocol_iap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\protocol_iap.c</FilePath>
            </File>
            <File>
              <FileName>user_vfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_vfs.c</FilePath>
            </File>
            <File>
              <FileName>common.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\common.h</FilePath>
            </File>
            <File>
              <FileName>common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\common.c</FilePath>
            </File>
            <File>
              <FileName>user_dev_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_dev_flash.c</FilePath>
            </File>
            <File>
              <FileName>user_dev_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_dev_uart.c</FilePath>
            </File>
            <File>
              <FileName>user_dev_bt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_dev_bt.c</FilePath>
            </File>
            <File>
              <FileName>dev_sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\dev_sys.c</FilePath>
            </File>
            <File>
              <FileName>bsp_ext_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\bsp_ext_flash.c</FilePath>
            </File>
            <File>
              <FileName>define.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\define.h</FilePath>
            </File>
            <File>
              <FileName>user_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_config.h</FilePath>
            </File>
            <File>
              <FileName>user_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\user\user_config.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_lib</GroupName>
          <Files>
            <File>
              <FileName>wlt_iot_ble_sdk.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\..\iot_ble_sdk\lib\wlt_iot_ble_sdk.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.6.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.7.7" url="http://www.keil.com/pack/" vendor="ARM" version="5.9.0"/>
        <targetInfos>
          <targetInfo name="ble_simple_peripheral"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

</Project>

#ifndef __EXTI_H__
#define __EXTI_H__

#include "sys_utils.h"

enum exti_inter_type
{
    EXTI_INTER_TYPE_LOW,    //低电平触发
    EXTI_INTER_TYPE_HIGH,   //高电平触发
    EXTI_INTER_TYPE_POS,    //上升沿触发
    EXTI_INTER_TYPE_NEG,    //下降沿触发
};

enum exti_channel
{
    EXTI_CHANNEL_0,
    EXTI_CHANNEL_1,
    EXTI_CHANNEL_2,
    EXTI_CHANNEL_3,
    EXTI_CHANNEL_4,
    EXTI_CHANNEL_5,
    EXTI_CHANNEL_6,
    EXTI_CHANNEL_7,
    EXTI_CHANNEL_8,
    EXTI_CHANNEL_9,
    EXTI_CHANNEL_10,
    EXTI_CHANNEL_11,
    EXTI_CHANNEL_12,
    EXTI_CHANNEL_13,
    EXTI_CHANNEL_14,
    EXTI_CHANNEL_15,
};

enum exti_mux_io
{
    EXTI_CHANNEL_0_PA0  = 0,
    EXTI_CHANNEL_0_PC0,
    EXTI_CHANNEL_0_PC7,

    EXTI_CHANNEL_1_PA1  = 0,
    EXTI_CHANNEL_1_PC1,
    EXTI_CHANNEL_1_PC6,

    EXTI_CHANNEL_2_PA2  = 0,
    EXTI_CHANNEL_2_PC2,
    EXTI_CHANNEL_2_PC5,

    EXTI_CHANNEL_3_PA3  = 0,
    EXTI_CHANNEL_3_PC3,
    EXTI_CHANNEL_3_PC4,

    EXTI_CHANNEL_4_PA4  = 0,
    EXTI_CHANNEL_4_PC4,
    EXTI_CHANNEL_4_PC3,

    EXTI_CHANNEL_5_PA5  = 0,
    EXTI_CHANNEL_5_PC5,
    EXTI_CHANNEL_5_PC2,

    EXTI_CHANNEL_6_PA6  = 0,
    EXTI_CHANNEL_6_PC6,
    EXTI_CHANNEL_6_PC1,

    EXTI_CHANNEL_7_PA7  = 0,
    EXTI_CHANNEL_7_PC7,
    EXTI_CHANNEL_7_PC0,

    EXTI_CHANNEL_8_PB0  = 0,
    EXTI_CHANNEL_8_PD0,
    EXTI_CHANNEL_8_PD7,

    EXTI_CHANNEL_9_PB1  = 0,
    EXTI_CHANNEL_9_PD1,
    EXTI_CHANNEL_9_PD6,

    EXTI_CHANNEL_10_PB2  = 0,
    EXTI_CHANNEL_10_PD2,
    EXTI_CHANNEL_10_PD5,

    EXTI_CHANNEL_11_PB3  = 0,
    EXTI_CHANNEL_11_PD3,
    EXTI_CHANNEL_11_PD4,

    EXTI_CHANNEL_12_PB4  = 0,
    EXTI_CHANNEL_12_PD4,
    EXTI_CHANNEL_12_PD3,

    EXTI_CHANNEL_13_PB5  = 0,
    EXTI_CHANNEL_13_PD5,
    EXTI_CHANNEL_13_PD2,

    EXTI_CHANNEL_14_PB6  = 0,
    EXTI_CHANNEL_14_PD6,
    EXTI_CHANNEL_14_PD2,

    EXTI_CHANNEL_15_PB7  = 0,
    EXTI_CHANNEL_15_PD7,
    EXTI_CHANNEL_15_PD1,
};

typedef void (*exti_callback)(uint32_t status);

/**
 * @brief   EXTI IO 初始化
 * 
 * @param   pin                     - gpio 引脚
 * 
 * @return  空
 */
void dev_exit_io_init(uint16_t pin);

/**
 * @brief   EXTI 外部中断使能
 * 
 * @param   channel                 - 中断channel
 * 
 * @return  空
 */
void dev_exti_int_enable(enum exti_channel channel);

/**
 * @brief   EXTI 外部中断关闭
 * 
 * @param   channel                 - 中断channel
 * 
 * @return  空
 */
void dev_exti_int_disable(enum exti_channel channel);

/**
 * @brief   EXTI 设置外部中断触发类型
 * 
 * @param   exti_channel            - 中断channel
 * @param   type                    - EXTI外部中断触发类型
 * 
 * @return  空
 */
void dev_exti_int_set_type(enum exti_channel exti_channel, enum exti_inter_type type);

/**
 * @brief   EXTI 设置外部中断IO口复用
 * 
 * @param   exti_channel            - 中断channel
 * @param   exti_io                 - 复用IO口
 * 
 * @return  空
 */
void dev_exti_int_set_port_mux(enum exti_channel exti_channel,enum exti_mux_io exti_io);

/**
 * @brief   EXTI 设置中断触发回调函数
 * 
 * @param   exti_callback_func_temp - 中断触发回调函数
 * 
 * @return  空
 */
void dev_set_exti_callback(exti_callback exti_callback_func_temp);
#endif /* __EXTI_H__ */


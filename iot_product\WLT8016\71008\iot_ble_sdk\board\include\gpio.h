#ifndef _GPIO_H_
#define _GPIO_H_
#include "wlt_ble_type.h"

//gpio 电平
#define PIN_LOW                 0x00
#define PIN_HIGH                0x01

//gpio 模式
#define PIN_MODE_OUTPUT         0x00
#define PIN_MODE_INPUT          0x01
#define PIN_MODE_INPUT_PULLUP   0x02

//gpio 引脚
#define IO_PA0              (0x00)
#define IO_PA1              (0x01)
#define IO_PA2              (0x02)
#define IO_PA3              (0x03)
#define IO_PA4              (0x04)
#define IO_PA5              (0x05)
#define IO_PA6              (0x06)
#define IO_PA7              (0x07)

#define IO_PB0              (0x10)
#define IO_PB1              (0x11)
#define IO_PB2              (0x12)
#define IO_PB3              (0x13)
#define IO_PB4              (0x14)
#define IO_PB5              (0x15)
#define IO_PB6              (0x16)
#define IO_PB7              (0x17)

#define IO_PC0              (0x20)
#define IO_PC1              (0x21)
#define IO_PC2              (0x22)
#define IO_PC3              (0x23)
#define IO_PC4              (0x24)
#define IO_PC5              (0x25)
#define IO_PC6              (0x26)
#define IO_PC7              (0x27)

#define IO_PD0              (0x30)
#define IO_PD1              (0x31)
#define IO_PD2              (0x32)
#define IO_PD3              (0x33)
#define IO_PD4              (0x34)
#define IO_PD5              (0x35)
#define IO_PD6              (0x36)
#define IO_PD7              (0x37)

#define PIN_PORT(pin) ((uint8_t)(pin/0x10))
#define PIN_NO(pin) ((uint8_t)((pin)%0x10))

/**
 * @brief   GPIO 模式配置
 * 
 * @param   pin                     - gpio 引脚
 * @param   mode                    - gpio 模式
 * 
 * @return  空
 */
void dev_gpio_mode(uint16_t pin, uint16_t mode);

/**
 * @brief   GPIO 电平操作
 * 
 * @param   pin                     - gpio 引脚
 * @param   value                   - gpio 电平
 * 
 * @return  空
 */
void dev_gpio_write(uint16_t pin, uint16_t value);

/**
 * @brief   GPIO 电平读取
 * 
 * @param   pin                     - gpio 引脚
 * 
 * @return  GPIO 电平
 */
int dev_gpio_read(uint16_t pin);

/**
* @brief   休眠唤醒IO作为普通IO使用前的初始化
 * 
 * @param   pin                     - gpio 引脚
 * 
 * @return  空
 */
void dev_wakeupgpio_reset(uint16_t pin);

/**
* @brief   休眠唤醒IO唤醒时电平读取
 * 
 * @param   pin                     - gpio 引脚
 * 
 * @return  GPIO 电平
 */
int dev_wakeupgpio_get(uint16_t pin);

/**
* @brief   休眠时IO口电平操作
 * 
 * @param   pin                     - gpio 引脚
 * @param   value                   - gpio 电平
 * 
 * @return  GPIO 电平
 */
void dev_sleepgpio_write(uint16_t pin, uint16_t value);
#endif /* _GPIO_H_ */

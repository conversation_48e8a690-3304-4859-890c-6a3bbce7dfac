#include "gpio.h"
#include "wlt_krnl_api.h"
#include "elog.h"
#include "wlt_util.h"
#include "system.h"
#include "user_vfs.h"
#include "common.h"
#include "service.h"
#include "rtc.h"
#include "product_test.h"
#include "ble_gap.h"
#include "time.h"
#include "watchdog.h"


#define SLEEP_ADV_CHANGE_TIME_MS   (10 * 60 * 1000) //10min  (10 * 60 * 1000)
#define SLEEP_ADV_CHANGE_TIMES     (SLEEP_ADV_CHANGE_TIME_MS / 200) //需要进入休眠广播的次数,200ms为默认广播间隔

extern uint8_t factory_out;
extern void dev_exti_sleep(void);


// void fleet_adv_interval_on(void);
// void fleet_adv_interval_off(void)//关闭广播。三分钟后开启
// {
// 	ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_OFF,NULL);
// 	if(com_wq_query(fleet_adv_interval_on)!=0)
// 	{
// 		com_wq_add(3*60*1000,fleet_adv_interval_on,WQ_PARAMETER_ONCE);
// 	}
// }
// void fleet_adv_interval_on(void)//打开广播。30秒后关闭
// {
// 	ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
// 	if(com_wq_query(fleet_adv_interval_off)!=0)
// 	{
// 		com_wq_add(30*1000,fleet_adv_interval_off,WQ_PARAMETER_ONCE);
// 	}
// }

/**
 * 出厂未被绑定过，默认以connect方式广播
 * 
 * fleet: -灵活广播
 *     有新数据时：持续广播,以200ms间隔
 *     无数据标志时：adv30s-停3min-adv30s-停3min....  300Ua-20Ua-300Ua-20Ua...
 * 
 * connect(dongle)：- 持续广播
 *     主机带电以200ms间隔广播
 *     主机下电以1000ms间隔广播：平均功耗78Ua;
 * 
 * 配网状态下开放20s窗口期（与fleet connect 及 host 无关）
 */
void sys_ble_adv_ctrl(void)
{
    dev_watchdog_feed();
    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);

/**
 * ******灵活广播策略******
 * uint8_t has_record=0;
    uint8_t is_fleet=0;
    uint8_t is_binding=0;
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R,&is_fleet);
    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_GET_BIND_STATUS,&is_binding);
    if(is_binding == 1)
    {
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
        com_wq_delete(fleet_adv_interval_on);
		com_wq_delete(fleet_adv_interval_off);
    }
    else
    {
        if(is_fleet == 1)
        {
            has_record=ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_HAS_DATA,NULL);
            if(has_record > 0)
            {
                ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
                com_wq_delete(fleet_adv_interval_on);
                com_wq_delete(fleet_adv_interval_off);
            }
            else
            {
                if(com_wq_query(fleet_adv_interval_on)!=0 && com_wq_query(fleet_adv_interval_off)!=0)
                {
                    fleet_adv_interval_on();
                }
            }
        }
        else 
        {
            com_wq_delete(fleet_adv_interval_on);
			com_wq_delete(fleet_adv_interval_off);
            ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
        }
    }
 */
}
/**
 * sleep模式下的广播模式控制
 */
static void sys_ble_adv_ctrl_insleep(void)
{
    dev_watchdog_feed();
    if(factory_out)
    {
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_OFF,NULL);
    }
    else
    {
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
    }

/**
 * ******灵活广播策略******
 * 
    #define ADV_MAX_MS_COUNT    (210*1000)
    #define ADV_MAX_ON_COUNT    (30*1000)//0-30:adv on 、 30-210:adv off
    #define ADV_INTERVAL_MS     (200)

    uint8_t has_record=0;
    uint8_t is_fleet=0;
    static uint32_t adv_ms=0;//记录广播开启、停止时间

    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_APP_IDENTIFY_R,&is_fleet);
    if(factory_out)
    {
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_OFF,NULL);
    }
    else
    {
        if(is_fleet == 1)
        {
            has_record=ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_HAS_DATA,NULL);
            if(has_record > 0)
            {
                ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
            }
            else
            {
                if(adv_ms<ADV_MAX_ON_COUNT){
                    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);//adv on以广播周期进行唤醒
                    adv_ms+=ADV_INTERVAL_MS;
                }
                else{
                    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_OFF,NULL);//adv off以rtc周期进行唤醒
                    adv_ms+=GET_RTC_INTERVAL_SLEEP*1000;
                }
                if(adv_ms>=ADV_MAX_MS_COUNT){
                    adv_ms=0;
                }
            }
        }
        else
        {
            ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_ADV_ON,NULL);
        }
    }
 */
    
}



static void rtc_timestamp_update(void)
{
    #define     RTC_SAVE_PERIOD     (60*60)//3600s
    #define     RTC_SAVE_VOLTAGE    (33)//3.3v
    static time_t timestamp_update_last=0;
    time_t sys_timestamp=0;
    uint8_t bat_vol=0;
    int ret = ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_RTC_R_STAMP,(uint8_t*)&sys_timestamp);
    // log_i("timestamp:%d,last:%d.",sys_timestamp,timestamp_update_last);
    if(ret >= 0){
        if((sys_timestamp-timestamp_update_last>=RTC_SAVE_PERIOD)){
            timestamp_update_last = sys_timestamp;
            ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_BAT_VOL,&bat_vol);
            if(bat_vol <= RTC_SAVE_VOLTAGE){
                ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_RTC_W_STAMP,NULL);
            }
        }
    }
}

void user_loop_callback(void)
{
    wlt_process_scheduler();
}

void user_wakeup_callback(void)
{
    uint8_t wakeup_io_level = 0;
    uint8_t bt_connect_status=0;

    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_WAKEUPIO_INSLEEP,&wakeup_io_level);
    ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_GET_CONN_STATUS,&bt_connect_status);
    
    if(wakeup_io_level != 0 || bt_connect_status==1)//高电平唤醒 or 蓝牙连接唤醒
    {
        IOT_LOG_I("user_wakeup_callback, io level.");
        wlt_set_user_loop_callback(&user_loop_callback);
        dev_exti_sleep();
        MSDK_init_from_sleep();
        ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_OUTSLEEP,NULL);
        ioctl(&bt_dev,DEV_IOCTL_BLE_CMD_SET_NORMAL_ADV_GAP,NULL);
        factory_out=0;
        return;
    }
    
    sys_ble_adv_ctrl_insleep();
    rtc_timestamp_update();
}

static void run_MSDK_service(void *param)
{
    MSDK_service();
    return;
}

void user_main(void)
{
    if (wlt_product_test_init((unsigned char *)PRODUCT_MODULE_NAME, (unsigned char *)PRODUCT_SOFTVERTION) == 0){
        /* 进入正常模式 */
        #if (LOG_SYSTEM == LOG_SYSTEM_ENABLE)
        elog_init();
        elog_set_fmt(ELOG_LVL_INFO, ELOG_FMT_LVL /*| ELOG_FMT_TAG */ | ELOG_FMT_FUNC /*| ELOG_FMT_DIR*/ | ELOG_FMT_LINE | ELOG_FMT_TIME);
        elog_start();
        #endif

        wlt_system_init(NULL);

        MSDK_init();

        /* scheduler */
        wlt_add_function_to_scheduler(run_MSDK_service, NULL, 1); //运行MSDK服务函数，1ms
        wlt_set_user_loop_callback(&user_loop_callback);
    }else{
        /* 进入产测模式 */
    }
}

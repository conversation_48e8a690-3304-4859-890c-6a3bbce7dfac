/**
 * Copyright (c) 2021, wi-linktech
 * 
 * All rights reserved.
 * 
 * 
 */
#ifndef __WLT_BLUETOOTH_H__
#define __WLT_BLUETOOTH_H__

#include "wlt_util.h"

typedef enum
{
   WLT_ATT_EVENT_MTU_EXCHANGE_COMPLETE, //MTU交换完成
   WLT_LE_CONNECTION_COMPLETE,          //做为BLE Slave 链接建立
   WLT_LE_DISCONNECTION_COMPLETE,       //链接断开
   WLT_LE_MASTER_CONNECTION_COMPLETE,   //做为BLE Master 链接建立
   WLT_LE_CONNECTION_UPDATE_COMPLETE,   //更新链接参数成功
   WLT_EVENT_LE_ADV_REPORT,             //做主扫描到周围设备上报
   WLT_RSSI_COMPLETE,                   //获取RSSI完成
   WLT_SMP_BOND_FAIL,                   //绑定失败
   WLT_SMP_BOND_SUCCESS,                //绑定成功
   WLT_SMP_MASTER_ENCRYPT_SUCCESS,      //加密成功
   WLT_SMP_MASTER_ENCRYPT_FAIL,         //加密失败
} bt_le_event_type_t;

typedef struct _tagwlt_att_event_mtu_exchange_complete_data_t
{
   uint16_t connection_handle; //BLE Connection Handle
   uint16_t mtu;               //MTU交换完成后确认的MTU大小
} wlt_att_event_mtu_exchange_complete_data_t;

typedef struct _tagwlt_le_device_connection_data_t
{
   uint16_t connection_handle;   //BLE Connection Handle
   uint8_t role;                 //role
   uint8_t peer_address_type;    // 链接对端的 mac 地址的类型。
   bd_addr_t RemoteDevice;       //链接对端 mac 地址。
   uint16_t conn_interval;       // 链接握手间隔参数。单位:1.25ms
   uint16_t conn_latency;        //链接  lantency 参数。
   uint16_t supervision_timeout; //链接超时断开的参数。单位:10ms
} wlt_le_device_connection_data_t;

typedef struct _tagwlt_le_device_disconnection_data_t
{
   uint16_t connection_handle; //BLE Connection Handle
   uint8_t reason;             // 断开链接的原因。参考 Bluetooth Core Specification
} wlt_le_device_disconnection_data_t;

typedef struct _tagwlt_le_connection_updata_complete_data_t
{
   uint16_t connection_handle;   //BLE Connection Handle
   uint16_t conn_interval;       // 链接参数更新后的握手间隔参数。单位:1.25ms
   uint16_t conn_latency;        //链接参数更新后的  lantency 参数。
   uint16_t supervision_timeout; //链接参数更新后的超时断开的参数。单位:10ms
} wlt_le_connection_updata_complete_data_t;

typedef struct _tagwlt_le_adv_report_data_t
{
   uint8_t adv_event_type;   //搜索到的广播事件类型
	 uint8_t adv_addr_type;		//搜索到的设备地址类型
	 bd_addr_t adv_addr;
	 int8_t  adv_rssi;
	 uint8_t adv_data_length;
	 uint8_t adv_data[31];
} wlt_le_adv_report_data_t;

typedef struct _tagwlt_rssi_complete_data_t
{
   int8_t rssi;               //对端设备的RSSI值
} wlt_rssi_complete_data_t;

typedef void (*bt_le_event_callback_t)(bt_le_event_type_t bt_le_event_type, void *event_data);

/**
 * @brief   BLE 设置应用层蓝牙事件回调函数
 * 
 * @param   bt_le_event_cb          - 应用层蓝牙事件回调函数
 * 
 * @return  空
 */
void bt_set_le_event_callback(bt_le_event_callback_t bt_le_event_cb);
#endif

#ifndef __UARTUPDATE_H__
#define __UARTUPDATE_H__

#include "sys_utils.h"
#include <stdbool.h>

enum uart_update_status
{
    NOT_UART_UPDATE_STATUS,
    UART_UPDATE_STATUS,
};

/**
 * @brief   串口升级设置状态
 * 
 * @param   state                   - 状态：UART_UPDATE_STATUS/NOT_UART_UPDATE_STATUS
 * 
 * @return  空
 */
void dev_uartupdate_set_state(enum uart_update_status state);

/**
 * @brief   获取串口升级状态
 * 
 * @param   空
 * 
 * @return  串口升级状态：UART_UPDATE_STATUS/NOT_UART_UPDATE_STATUS
 */
enum uart_update_status dev_uartupdate_get_state(void);

/**
 * @brief   获取将要串口升级固件起始地址
 * 
 * @param   空
 * 
 * @return  将要串口升级固件起始地址
 */
unsigned int dev_uartupdate_get_storage_address(void);

/**
 * @brief   获取Image Size
 * 
 * @param   空
 * 
 * @return  Image Size
 */
unsigned int dev_uartupdate_get_image_size(void);

/**
 * @brief   获取当前运行固件版本号
 * 
 * @param   空
 * 
 * @return  当前运行固件版本号
 */
unsigned int dev_uartupdate_get_curr_firmwave_version(void);

/**
 * @brief   当前固件版本号加一
 * 
 * @param   data                   - 接收固件前256个字节数组
 * 
 * @return  空
 */
void dev_uartupdate_firmware_version_add(unsigned char *data);

/**
 * @brief   向Flash存储固件
 * 
 * @param   dest                   - 地址
 * @param   src                    - 写入数据数组
 * @param   len                    - 写入数据长度
 * 
 * @return  DEV_FLASH_OPERA_SUCCESS：flash写入地址在可操作地址范围内
 *          DEV_FLASH_OPERA_Fail：flash写入地址不在可操作地址范围内
 */
int dev_uartupdate_save_data(unsigned int dest, uint8_t *src, unsigned int len);

/**
 * @brief   从Flash读取固件
 * 
 * @param   dest                   - 地址
 * @param   src                    - 读取数据数组
 * @param   len                    - 读取数据长度
 * 
 * @return  空
 */
void dev_uartupdate_read_data(unsigned int dest, uint8_t *src, unsigned int len);

/**
 * @brief   判断CRC校验值是否一致
 * 
 * @param   firmware_length                     - 固件长度
 * @param   new_bin_addr                        - 更新固件地址
 * @param   crc_data_t                          - CRC校验值
 * 
 * @return  CRC校验值是否一致:1校验成功 0校验失败
 */
uint8_t dev_uartupdate_crc_cal(uint32_t firmware_length,uint32_t new_bin_addr,uint32_t crc_data_t);

/**
 * @brief   串口升级擦除函数
 * 
 * @param   offset                   	- 地址
 * @param   size                    	- 擦除大小：4K的整数倍
 * 
 * @return  DEV_FLASH_OPERA_SUCCESS：flash擦除地址在可操作地址范围内
 *          DEV_FLASH_OPERA_Fail：flash擦除地址不在可操作地址范围内
 */
int dev_uartupdate_erase_data(uint32_t offset, uint32_t size);

/**
 * @brief   串口升级擦除256字节函数
 * 
 * @param   offset                   	- 地址
 * 
 * @return  DEV_FLASH_OPERA_SUCCESS：flash擦除地址在可操作地址范围内
 *          DEV_FLASH_OPERA_Fail：flash擦除地址不在可操作地址范围内
 */
int dev_uartupdate_page_erase_data(uint32_t offset);
#endif /* __UARTUPDATE_H__ */


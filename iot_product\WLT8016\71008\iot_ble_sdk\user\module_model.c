#include "common.h"
#include "module.h"
#include "user_vfs.h"

/**************************
* 关于物模型的收发逻辑
**************************/


#define MODULE_LISTENER     (16)

#define DPMSG_NUM   (20)
typedef struct dpmsg_element_struct {
    uint16_t dp_id;
    uint8_t dp_type;
    uint16_t len;
    uint8_t *data;
}dpmsg_element_t;

typedef struct dpmsg_all_struct {
    uint8_t num;
    dpmsg_element_t element[DPMSG_NUM];
}dpmsg_all_t;

static dpmsg_all_t dpmsg_all;

typedef enum {
    CONTINUE_START = 0,
    CONTINUE_NEXT,
} cont_history_e;

typedef enum
{
    dpid_None=0,
    //attribution
    dpid_otastatus              = 1001, //允许OTA升级,enum
    dpid_allWorkTime            = 1016, //累计工作时长,int,s
    dpid_DCDCWorkState          = 2017, //DCDC工作状态,enum
    dpid_ACChargeRemain         = 2021, //AC模式充满剩余时间,int,s
    dpid_ACChargeProgress       = 2022, //AC模式充电进度,int,0-100 %
    dpid_ACChargeTotalTime      = 2023, //AC模式累计充电时长,int,s
    dpid_eachBatteryEnergy      = 2024, //各电池包剩余电量,raw,mAh;大包4Bytes+小包4Bytes+小包4Bytes
    
    dpid_DCChargeRemain         = 2025, //DC模式充满剩余时间,raw,s;小包4Bytes+小包4Bytes
    dpid_DCChargeTotalTime      = 2026, //DC累计充电时长,int,s
    dpid_totalChargeEnergy      = 2027, //总充电能量,int,kWh
    dpid_TotalDischargeEnergy   = 2035, //总放电能量,int,kWh
    dpid_pass7dChargeEngy       = 2036, //过去7天充电能量,raw,kWh;uint16*7
    dpid_pass12mChargeEngy      = 2037, //过去12个月充电能量,raw,kWh;uint16*12
    dpid_pass7dDischargeEngy    = 2038, //过去7天放电能量,raw,kWh;uint16*7
    dpid_pass12mDischargeEngy   = 2039, //过去12个月放电能量,raw,kWh;uint16*12
    //service
    dpid_OnOffDCDCMode          = 21010, //DC-DC模式开关
    dpid_DfineChargeSeq         = 21011, //自定义电池包充电顺序
    dpid_OnOffPerf              = 21004, //电池优化开关
    //event
    dpid_FaultMessages          = 41003, //错误信息上报
} dpid_e;

#if (IOT_T_MODEL_IN_OTA == 0)
static uint8_t on_ota_down = 0; //1:当前正在从APP OTA下载数据，不可通信; 其他皆可通信
#endif
static uint8_t has_history_data = 0; //0:无历史数据，1有历史数据
static uint8_t app_is_fleet = 0; //>0: 当前app是fleet; =0: 当前app为ego connect
static uint8_t app_identif = 0; //>0: 当前认证通过；=0：当前认证未通过
static uint8_t host_is_on = 0; //当前主机是否上电，>0上电 ==0 掉电
/*************************************************
* static base operations
*************************************************/
void dpmsg_unpack(uint8_t *data, uint16_t len)
{
    uint16_t len_check = 0;
    memset((void*)(&dpmsg_all),0,sizeof(dpmsg_all_t));
    while(1){
        if(len_check >= len){
            break;
        }
        uint16_t index = dpmsg_all.num;
        dpmsg_all.element[index].dp_id =
            ((uint16_t)data[len_check+0]<<8)|(data[len_check+1]);
        dpmsg_all.element[index].dp_type = data[len_check+2];
        dpmsg_all.element[index].len =
            ((uint16_t)data[len_check+3]<<8)|(data[len_check+4]);
        dpmsg_all.element[index].data = &(data[len_check+5]);
        len_check += (5 + dpmsg_all.element[index].len);
        dpmsg_all.num ++;
        if(dpmsg_all.num >= DPMSG_NUM){
            IOT_LOG_I("dp msg too much.");
            break;
        }
    }
}

/*************************************************
* work queue operations
*************************************************/

/*************************************************
* event operations
*************************************************/
static void event_ota_start(void * data)
{
    #if (IOT_T_MODEL_IN_OTA == 0)
    on_ota_down = 1;
    #endif
}

static void event_ota_end(void * data)
{
    #if (IOT_T_MODEL_IN_OTA == 0)
    on_ota_down = 0;
    #endif
}

static void event_ble_disconnect(void * data)
{
    app_is_fleet = 0;
    app_identif = 0;
    IOT_LOG_I("app identify close.");
}

static void event_id_is_fleet(void * data)
{
    app_is_fleet = 1;
    IOT_LOG_I("app_is_fleet = 1.");
}

static void event_id_identif_pass(void * data)
{
    proto_frame_t pframe;
    if(app_identif == 0){
        pframe.len = 0;
        pframe.cmd = PROTO_CMD_DP_ALL_CHECK;
        pframe.direction = PROTO_DIRECTION_MCU;
        pframe.pdata_origin = g_buf_MCU;
        protocol_pack(&pframe);
        write(&uart_dev, pframe.pdata_origin, pframe.len_origin);
        IOT_LOG_I("send all check cmd.");
    }
    app_identif = 1;
    IOT_LOG_I("app identify pass.");
}

static void event_host_is_on(void * data)
{
    host_is_on = 1;
    IOT_LOG_I("host_is_on = 1.");
}

static void event_host_is_off(void * data)
{
    host_is_on = 0;
    IOT_LOG_I("host_is_on = 0.");
}


/**
 * 依赖模组上报的物模型，统一上报函数
 * 
 * 加入设备类型判断。部分主机有外置的flash，可自行上报total类数据。物模型由主机侧上报
 * 对于主机侧无法上报total类数据的主机，由模组处理汇总上报:
 * 81010        SNcode：ST29
 * 83006        SNcode：HT13
 * 71007        SNcode：LM28
 * 71008/71011  SNcode：LM29
 */
static void module_model_report(void)
{
    int code = 0;
    bool send_flag=false;
    uint16_t dp_len=0;
    uint8_t dp_data[256]={0};
    proto_frame_t pframe;

    total_info_t total_info={0};
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_TOTAL_R,(uint8_t*)&total_info);
    // log_i("readread:%d,%d,%d,%d....",total_info.valid_code, total_info.Last_worktime,total_info.total_worktime,total_info.total_power_consumption);
    char sn_data[PRODUCT_INFO_SN_LEN_MAX]={0};
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_SN_R,(uint8_t*)&(sn_data[0]));
    // log_i("sn_data = %s.",sn_data);

    //1027  - 所有产品都要添加
    dp_data[dp_len++]=(LAST_WORK_DPID>>8)&0xff;
    dp_data[dp_len++]=(LAST_WORK_DPID>>0)&0xff;
    dp_data[dp_len++]=DP_TYPE_VALUE;
    dp_data[dp_len++]=0x00;
    dp_data[dp_len++]=0x04;
    memcpy(&dp_data[dp_len],&total_info.Last_worktime,4);
    dp_len+=4;

    for (uint8_t i = 0; i < product_total_list.count; i++)
    {
        code = strncmp(sn_data,product_total_list.sn_list[i].sn_code,LEN_SN);
        if(code == 0)
        {
            send_flag = true;
            break;
        }
    }
    if(send_flag == true)
    {
        //1016
        dp_data[dp_len++]=(DPID_TOTAL_WORK_TIME>>8)&0xff;
        dp_data[dp_len++]=(DPID_TOTAL_WORK_TIME>>0)&0xff;
        dp_data[dp_len++]=DP_TYPE_VALUE;
        dp_data[dp_len++]=0x00;
        dp_data[dp_len++]=0x04;
        memcpy(&dp_data[dp_len],&total_info.total_worktime,4);
        dp_len+=4;

        //1025
        dp_data[dp_len++]=(DPID_TOTAL_POWER_CONSUMPTION>>8)&0xff;
        dp_data[dp_len++]=(DPID_TOTAL_POWER_CONSUMPTION>>0)&0xff;
        dp_data[dp_len++]=DP_TYPE_VALUE;
        dp_data[dp_len++]=0x00;
        dp_data[dp_len++]=0x04;
        memcpy(&dp_data[dp_len],&total_info.total_power_consumption,4);
        dp_len+=4;
    }

    if(dp_len > 256)
    {
        IOT_LOG_I("data is to long.");
        return;
    }
    pframe.cmd = PROTO_CMD_DP_UPLOAD;
    pframe.pdata = dp_data;
    pframe.len = dp_len;
    pframe.direction = PROTO_DIRECTION_APP;
    pframe.pdata_origin = g_buf_APP;
    protocol_pack(&pframe);
    write(&bt_dev, pframe.pdata_origin, pframe.len_origin);
}


static void add_ts(void *data)
{
    //模组给记录打时间戳
    proto_frame_t* pframe = data;
    uint32_t    sys_stamp=0;
    uint16_t    dpid=pframe->pdata[0]<<8|pframe->pdata[1];
    if(dpid==TIMESTAMP_DPID)
    {
        ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_RTC_R_STAMP,(uint8_t *)&sys_stamp);
        com_ending_32_pack(1,sys_stamp,&pframe->pdata[5]);
    }
}

static int update_total_data(dp_list_t *dp)
{
    if(dp == NULL || dp->dp_num == 0)
    {
        IOT_LOG_I("point is null.");
        return -1;
    }
    
    // log_i("dp->num:%d",dp->dp_num);

    total_info_t total_info={0};
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_TOTAL_R,(uint8_t*)&total_info);
    // log_i("read:%d,%d,%d,%d....",total_info.valid_code,total_info.Last_worktime,total_info.total_worktime,total_info.total_power_consumption);
    for (uint8_t i = 0; i < dp->dp_num; i++)
    {
        // log_i("id:%d,value:%d",dp->dp_list[i].dp_id,dp->dp_list[i].dp_value);
        switch (dp->dp_list[i].dp_id)
        {
            case TIMESTAMP_DPID:
                total_info.Last_worktime = dp->dp_list[i].dp_value;
                break;
            case DPID_SINGLE_WORK_TIME:
                total_info.total_worktime += dp->dp_list[i].dp_value;
                break;
            case DPID_SINGLE_POWER_CONSUMPTION:
                total_info.total_power_consumption += dp->dp_list[i].dp_value;
                break;
            
            default:
                break;
        }
    }
    // log_i("%d,%d,%d,%d....",total_info.valid_code,total_info.Last_worktime,total_info.total_worktime,total_info.total_power_consumption);
    ioctl(&sys_dev,DEV_IOCTL_SYS_CMD_TOTAL_W,(uint8_t*)&total_info);
    return 0;
}

/*************************************************
* cmd operations
*************************************************/
/*
* 接收APP下发的物模型,转发给MCU
*/
static void dp_check_cb(void * data)
{
    IOT_LOG_I("enter.");
    proto_frame_t* pframe = data;
    if(app_identif == 0){
        IOT_LOG_I("need identify.");
        return;
    }
    memcpy(g_buf_MCU,pframe->pdata_origin,pframe->len_origin);
    write(&uart_dev, g_buf_MCU, pframe->len_origin);
    IOT_LOG_I("query some fun BT -> uart.");
}


/*
* 接收MCU上报的物模型。从MCU接收，发送到APP，并回复ACK给MCU
*/
static void dp_upload_cb(void * data)
{
    IOT_LOG_I("dp_upload_cb.");
    proto_frame_t* pframe = data;
    if(app_identif == 0){
        IOT_LOG_I("need identify.");
        return;
    }

    #if (IOT_T_MODEL_IN_OTA == 0)
        if(on_ota_down == 1) //1:当前正在从APP OTA下载数据，不可通信; 其他皆可通信
        {
            return;
        }
    #endif

    add_ts(data);

    pframe->direction = PROTO_DIRECTION_APP;
    pframe->pdata_origin = g_buf_APP;
    protocol_pack(pframe);
    write(&bt_dev, pframe->pdata_origin, pframe->len_origin);

    pframe->len = 1;
    pframe->pdata[0] = 0;
    pframe->direction = PROTO_DIRECTION_MCU;
    pframe->pdata_origin = g_buf_MCU;
    protocol_pack(pframe);
    write(&uart_dev, g_buf_MCU, pframe->len_origin);
}

static int wq_upload_history_to_app(uint8_t flag);
static void wq_upload_history_to_app_cb(void)//主动发送记录
{
    wq_upload_history_to_app(CONTINUE_START);
}

/*
* 查看所有物模型。从APP收到指令，发送到MCU
*/
static void dp_allcheck_cb(void * data)
{
    IOT_LOG_I("enter.");
    proto_frame_t* pframe = data;
    if(app_identif == 0){
        IOT_LOG_I("need identify.");
        return;
    }
    memcpy(g_buf_MCU,pframe->pdata_origin,pframe->len_origin);
    write(&uart_dev, g_buf_MCU, pframe->len_origin);

    module_model_report();
    if(app_is_fleet == 0)//connect app 2s后开始上报原子数据记录
    {
        com_wq_add(2000,wq_upload_history_to_app_cb,WQ_PARAMETER_ONCE);
    }
    else//fleet app 
    {
        if(host_is_on == 0)//且主机关机状态，返回0长度 0x07
        {
            pframe->len = 0;
            pframe->cmd = PROTO_CMD_DP_UPLOAD;
            pframe->direction = PROTO_DIRECTION_APP;
            pframe->pdata_origin = g_buf_APP;
            protocol_pack(pframe);
            write(&bt_dev, pframe->pdata_origin, pframe->len_origin);
            return;
        }
        else
        {
            //透传至mcu全查,上面已发送。此处nothing
            return;
        }
    }
}

static void wq_clear_history(void)
{
    if(has_history_data != 0){
        send_eq(EVENT_HIS_NO_DATA); //事件告知无新数据了
        has_history_data = 0;
    }
}

// para: using cont_history_e
static int wq_upload_history_to_app(uint8_t flag)
{
    int ret = 0;
    uint8_t sdata[256];
    proto_frame_t frame;
    frame.pdata = sdata;
    uint32_t ret_len = 0;
    dev_data_flash_t ftmp;
    ftmp.data = frame.pdata;
    ftmp.ret_len = &ret_len;
    if(flag == CONTINUE_START){
        ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_GET_CONTINUE_START,(uint8_t*)&ftmp);
    }else{
        ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_GET_CONTINUE,(uint8_t*)&ftmp);
    }
    if(ret < 0){
        //当前无数据，获取失败
        frame.len = 0;
    }else{
        //当前有数据，获取成功
        frame.len = ret_len;
    }
    frame.direction = PROTO_DIRECTION_APP;
    frame.pdata_origin = g_buf_APP;
    frame.cmd = PROTO_CMD_UP_HISTORY;
    protocol_pack(&frame);
    write(&bt_dev, frame.pdata_origin, frame.len_origin);
    if(ret < 0){
        wq_clear_history();
    }
    IOT_LOG_HEX_DUMP("get continue",16,frame.pdata_origin,frame.len_origin);
    return ret;
}

static void wq_upload_diagnosis_history_to_app(void)
{
    int ret = 0;
    uint8_t sdata[256];
    proto_frame_t frame;
    frame.pdata = sdata;
    uint32_t ret_len = 0;
    dev_data_flash_t ftmp;
    ftmp.data = frame.pdata;
    ftmp.ret_len = &ret_len;
    ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_DIAG_CONTINUE,(uint8_t*)&ftmp);
    if(ret < 0){
        //当前无数据，获取失败
        frame.len = 0;
    }else{
        //当前有数据，获取成功
        frame.len = ret_len;
    }
    frame.direction = PROTO_DIRECTION_APP;
    frame.pdata_origin = g_buf_APP;
    frame.cmd = PROTO_CMD_UP_HISTORY;
    protocol_pack(&frame);
    write(&bt_dev, frame.pdata_origin, frame.len_origin);
    if(ret < 0){
        com_wq_delete(wq_upload_diagnosis_history_to_app);
    }
    IOT_LOG_HEX_DUMP("get continue",16,frame.pdata_origin,frame.len_origin);
}

/*
* APP向模组请求历史数据上报
*/
static void history_request_from_app_cb(void * data)
{
    IOT_LOG_I("history_request_from_app_cb.");
    proto_frame_t* pframe = data;
    if(app_identif == 0){
        IOT_LOG_I("need identify.");
        return;
    }
    if(pframe->len == 0){
        //首次查询
        wq_upload_history_to_app(CONTINUE_START);
    }else if(pframe->len == 1 && pframe->pdata[0] == 1){
        //ACK 回复，（来自APP的确认消息）
        wq_upload_history_to_app(CONTINUE_NEXT);
    }
}

/*
* MCU向模组&模组向APP上报累计数据
*/
static void history_from_mcu_cb(void * data)
{
    IOT_LOG_I("history_from_mcu_cb.");
    int ret = 0;
    proto_frame_t* pframe = data;
    dev_data_flash_t ftmp;

    //1.add 时间戳
    add_ts(data);

    //2.解析提取dp、更新total类数据  -所有型号都会在模组内累加total类数据，按型号选择是否上报
    dp_list_t dp={0};
    user_hex_to_dp(pframe->pdata,pframe->len,&dp);
    update_total_data(&dp);
    com_wq_add(1000,module_model_report,WQ_PARAMETER_ONCE);//上报total类数据

    //3.记录上报、存储
    if(app_identif != 0 && app_is_fleet == 0)
    {
        //app连接状态，且不是fleet app
        //2025年6月25日15:15:07：单次数据全部通过0x17上报(/batch topic)
        pframe->cmd = PROTO_CMD_UP_HISTORY;
        pframe->direction = PROTO_DIRECTION_APP;
        pframe->pdata_origin = g_buf_APP;
        protocol_pack(pframe);
        write(&bt_dev, pframe->pdata_origin, pframe->len_origin);
    }
    else
    {
        //未上报情况，存储下来
        ftmp.data = pframe->pdata;
        ftmp.len = pframe->len;
        ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_PUSH,(uint8_t*)&ftmp);

        if(has_history_data == 0)
        {
            send_eq(EVENT_HIS_HAS_DATA); //事件告知有新数据了
            has_history_data = 1;
        }
    }
    
    //4.回复给MCU
    pframe->len = 1;
    pframe->pdata[0] = ret==0?0:1;
    pframe->cmd=PROTO_CMD_UP_HISTORY;
    pframe->direction = PROTO_DIRECTION_MCU;
    pframe->pdata_origin = g_buf_MCU;
    protocol_pack(pframe);
    write(&uart_dev, pframe->pdata_origin, pframe->len_origin);
}


/*
* MCU向模组拉取工具累计数据
*/
static void history_request_from_mcu_cb(void * data)
{
    IOT_LOG_I("history_request_from_mcu_cb.");
    proto_frame_t* pframe = data;
    int ret = 0;
    uint32_t ret_len = 0;
    dev_data_flash_t ftmp;
    ftmp.data = pframe->pdata;
    ftmp.ret_len = &ret_len;
    ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_GET_LAST,(uint8_t*)&ftmp);
    if(ret < 0){
        //当前无数据，获取失败
        if(ret == -2){
            //当前读取失败
            pframe->len = 1;
            pframe->pdata[0] = 1; //读取失败
        }else{
            //记录为空
            pframe->len = 1;
            pframe->pdata[0] = 2; //记录为空
        }
    }else{
        //当前有数据，获取成功
        pframe->len = ret_len;
    }
    pframe->direction = PROTO_DIRECTION_MCU;
    pframe->pdata_origin = g_buf_MCU;
    protocol_pack(pframe);
    write(&uart_dev, pframe->pdata_origin, pframe->len_origin);
    IOT_LOG_HEX_DUMP("get",16,pframe->pdata_origin,pframe->len_origin);
}

/*
* APP向模组拉取所有历史数据用于诊断
*/
static void history_diagnose_from_app_cb(void * data)
{
    IOT_LOG_I("history_diagnose_from_app_cb.");
    com_wq_add(50,wq_upload_diagnosis_history_to_app,WQ_PARAMETER_CYCLE); //50ms上传一条
}

/*
* APP向模组查询未读取的历史记录数量，设备主动通知APP当前有记录待取。
*/
static void history_record_cnt_from_app_cb(void * data)
{
    IOT_LOG_I("history_record_cnt_from_app_cb.");
    
    proto_frame_t *pframe=data;
    dev_data_flash_t ftmp;
    ftmp.data = pframe->pdata;
    ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_COUNT,(uint8_t*)&ftmp);

    pframe->len = 4;
    pframe->direction = PROTO_DIRECTION_APP;
    pframe->pdata_origin = g_buf_APP;
    pframe->cmd = PROTO_CMD_MCU_GET_HISTORY;
    protocol_pack(pframe);
    write(&bt_dev, pframe->pdata_origin, pframe->len_origin);
    return;
}



/*************************************************
* module operations
*************************************************/
/* event处理 
*  return: >=0 process; <0: no executed
*/
static int m_do_event(uint8_t event)
{
    int ret = -1;
    mlist_element_t* plist = module_model.plist;
    for(int i = 0; i < MODULE_LISTENER; ++i){
        if(PARA_DECODE_EVENT(plist[i].para) == event){
            ret = event;
            plist[i].cb(&event);
        }
    }
    return ret;
}
/* msg处理 
*  return: >=0 process; <0: no executed
*/
static int m_do_cmd(proto_frame_t * pframe)
{
    int ret = -1;
    if(pframe == NULL){
        IOT_LOG_E("pframe is null.");
        return -1;
    }
    int cmd =  pframe->cmd;
    int from = pframe->direction;
    mlist_element_t* plist = module_model.plist;
    for(int i = 0; i < MODULE_LISTENER; ++i){
        if(PARA_DECODE_EVENT(plist[i].para)>0){
            continue;
        }
        if(PARA_DECODE_CMD(plist[i].para) == cmd
            && PARA_DECODE_FROM(plist[i].para) == from)
            // && (PARA_DECODE_FROM(plist[i].para) == from || PARA_DECODE_FROM(plist[i].para) == PROTO_DIRECTION_ALL)
        {
            ret = cmd;
            plist[i].cb(pframe);
        }
    }
    return ret;
}
/* 初始化 */
static void m_init(void)
{
    int ret = 0;
    IOT_LOG_I("enter.");
    ret = ioctl(&flash_dev,DEV_IOCTL_FLASH_CMD_HISTORY_HAS_DATA,NULL);
    if(ret > 0){ //有数据
        send_eq(EVENT_HIS_HAS_DATA); //事件告知有历史数据
        has_history_data = 1;
    }else{
        send_eq(EVENT_HIS_NO_DATA); //事件告知无历史数据
        has_history_data = 0;
    }
}


static mlist_element_t module_list[MODULE_LISTENER] = {
{PARA_EVENT(EVENT_OTA_DOWN_START),  event_ota_start},
{PARA_EVENT(EVENT_OTA_DOWN_END),    event_ota_end},
{PARA_EVENT(EVENT_BLE_DISCONNECT),  event_ble_disconnect},
{PARA_EVENT(EVENT_ID_IS_FLEET),     event_id_is_fleet},
{PARA_EVENT(EVENT_ID_IDENTIF_PASS), event_id_identif_pass},
{PARA_EVENT(EVENT_HOST_ON),         event_host_is_on},
{PARA_EVENT(EVENT_HOST_OFF),        event_host_is_off},
{PARA_FROM(PROTO_DIRECTION_APP) | PARA_CMD(PROTO_CMD_DP_CHECK),      dp_check_cb},
{PARA_FROM(PROTO_DIRECTION_MCU) | PARA_CMD(PROTO_CMD_DP_UPLOAD),     dp_upload_cb},
{PARA_FROM(PROTO_DIRECTION_APP) | PARA_CMD(PROTO_CMD_DP_ALL_CHECK),  dp_allcheck_cb},
{PARA_FROM(PROTO_DIRECTION_APP) | PARA_CMD(PROTO_CMD_REQ_HISTORY),      history_request_from_app_cb},
{PARA_FROM(PROTO_DIRECTION_MCU) | PARA_CMD(PROTO_CMD_UP_HISTORY),       history_from_mcu_cb},
{PARA_FROM(PROTO_DIRECTION_MCU) | PARA_CMD(PROTO_CMD_MCU_GET_HISTORY),  history_request_from_mcu_cb},
{PARA_FROM(PROTO_DIRECTION_APP) | PARA_CMD(PROTO_CMD_DIAGNOSE_HISTORY),  history_diagnose_from_app_cb},
{PARA_FROM(PROTO_DIRECTION_APP) | PARA_CMD(PROTO_CMD_MCU_GET_HISTORY),  history_record_cnt_from_app_cb},
};

module_t module_model = {
    .count = MODULE_LISTENER,
    .plist = module_list,
    .init = m_init,
    .do_event = m_do_event,
    .do_cmd = m_do_cmd,
};

#ifndef __WLT_BLE_WLT6200_H__
#define __WLT_BLE_WLT6200_H__
#include "wlt_util.h"
#include "wlt_ble_type.h"

enum ble_smp_mode
{
    NO_PARING,  //不支持安全加密配对
    JUST_WORK,  //just work模式
    PASSKEY,    //输入Pincode模式
};

/**
 * @brief   BLE 协议栈初始化
 * 
 * @param   空
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_stack_init(void);
// wlt_ble_status_t wlt_ble_mainloop(void);

/**
 * @brief   BLE 设置广播数据
 * 
 * @param   advertising_data_length             - 广播数据长度
 * @param   advertising_data                    - 广播数据
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_gap_advertisements_set_data(uint8_t advertising_data_length, uint8_t *advertising_data);

/**
 * @brief   BLE 设置扫描应答数据
 * 
 * @param   scan_response_data_length           - 广播数据长度
 * @param   scan_response_data                  - 广播数据
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_gap_scan_response_set_data(uint8_t scan_response_data_length, uint8_t *scan_response_data);

/**
 * @brief   BLE 设置广播间隔
 * 
 * @param   adv_int_min                         - 广播间隔最小值
 * @param   adv_int_max                         - 广播间隔最大值
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_gap_advertisements_set_params(uint16_t adv_int_min, uint16_t adv_int_max);

/**
 * @brief   BLE 打开或关闭广播
 * 
 * @param   enabled                             - 1：打开广播/0：关闭广播
 * 
 * @return  空
 */
void wlt_gap_advertisements_enable(int enabled);

/**
 * @brief   BLE 请求连接参数更新
 * 
 * @param   ble_con_handle                      - ble连接handle
 * @param   conn_interval_min                   - 连接间隔最小值 单位：1.25ms
 * @param   conn_interval_max                   - 连接间隔最大值 单位：1.25ms
 * @param   conn_latency                        - 从机延迟
 * @param   supervision_timeout                 - 超时时间 单位：10ms
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_gap_request_connection_parameter_update(uint8_t ble_con_handle, uint16_t conn_interval_min, uint16_t conn_interval_max, uint16_t conn_latency, uint16_t supervision_timeout);
//wlt_ble_status_t wlt_ble_set_device_name(const uint8_t *buf, uint16_t size);

/**
 * @brief   BLE 断开连接
 * 
 * @param   ble_con_handle                      - ble连接handle
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gap_disconnect(uint16_t ble_con_handle);

/**
 * @brief   BLE 获取地址
 * 
 * @param   blue_addr                           - 获取ble地址保存buffer首地址
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gap_addr_get(bd_addr_t *blue_addr);

/**
 * @brief   BLE notify数据
 * 
 * @param   con_handle                          - ble连接handle
 * @param   attribute_handle                    - notify handle
 * @param   value                               - notify 数据buffer首地址
 * @param   value_len                           - notify 数据长度
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gatt_notify_data(uint16_t con_handle, uint16_t attribute_handle, uint8_t *value, uint16_t value_len);

/**
 * @brief   BLE notify数据
 * 
 * @param   con_handle                          - ble连接handle
 * @param   attribute_handle                    - notify handle
 * @param   value                               - notify 数据buffer首地址
 * @param   value_len                           - notify 数据长度
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gatt_indicate_data(uint16_t con_handle, uint16_t attribute_handle, uint8_t *value, uint16_t value_len);

/**
 * @brief   设置本地设备名称
 * 
 * @param   local_name                          - 本地设备名称buffer首地址
 * @param   len                                 - 本地设备名称长度
 * 
 * @return  空
 */
void wlt_ble_gap_set_dev_name(uint8_t *local_name,uint8_t len);

/**
 * @brief   BLE 做主开始扫描
 * 
 * @param   scan_type                           - 扫描类型：0/1 0:主动扫描 能收到scan rsp包 1:被动扫描 不能收到scan rsp包
 * @param   scan_interval                       - 扫描间隔 (单位为625us) 范围(4 - 16384)  
 * @param   scan_window                         - 扫描窗口 (单位为625us) 必须<= scan_interval 范围[4 - 16384]  
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_master_gap_start_scan(uint8_t scan_type, uint16_t scan_interval, uint16_t scan_window);

/**
 * @brief   BLE 做主停止扫描
 * 
 * @param   空
 * 
 * @return  空
 */
void wlt_ble_master_gap_stop_scan(void);

/**
 * @brief   BLE 做主发送数据
 * 
 * @param   con_handle                          - ble连接handle
 * @param   attribute_handle                    - 写数据handle
 * @param   value                               - 数据buffer首地址
 * @param   value_len                           - 数据长度
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_master_gatt_master_write_data(uint16_t con_handle, uint16_t attribute_handle, uint8_t *value, uint16_t value_len);

/**
 * @brief   BLE 做主发起连接
 * 
 * @param   blue_addr                           - 对端地址
 * @param   addr_type                           - 对端地址类型
 * @param   conn_interval_min                   - 最小连接间隔 单位：1.25ms
 * @param   conn_interval_max                   - 最大连接间隔 单位：1.25ms
 * @param   conn_latency                        - 从机延迟
 * @param   supervision_timeout                 - 超时时间 单位：10ms
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_master_gap_connect(unsigned char *blue_addr, uint8_t addr_type,uint16_t conn_interval_min, uint16_t conn_interval_max, uint16_t conn_latency, uint16_t supervision_timeout);

/**
 * @brief   BLE 发起更新MTU请求
 * 
 * @param   con_handle                          - ble连接handle (模块MTU最大259) 
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gatt_mtu_exchange_req(uint8_t con_handle);

/**
 * @brief   BLE 设置安全加密配对模式
 * 
 * @param   smpmode                             - 安全加密配对模式
 * @param   pincode                             - PASSKEY模式对应PINCODE 范围：000000 - 999999
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_smp_mode(enum ble_smp_mode smpmode,unsigned int pincode);

/**
 * @brief   BLE 发现对端服务
 * 
 * @param   con_handle                          - ble连接handle
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gatt_discovery_peer_svc(uint8_t con_handle);

/**
 * @brief   BLE 主设备直接注册通信服务
 * 
 * @param   con_handle                          - ble连接handle
 * @param   start_handle                        - 启动handle值
 * @param   end_handle                          - 结束handle值
 * @param   notify_att_nb                       - notify handle值
 * @param   notify_att_handle                   - notify handle数量 固定为1
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gatt_client_direct_register(uint8_t con_handle, uint16_t start_handle, uint16_t end_handle, uint8_t notify_att_nb, uint16_t notify_att_handle);

/**
 * @brief   BLE 主设备订阅notify服务
 * 
 * @param   con_handle                          - ble连接handle
 * @param   handle                              - handle值
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_gatt_client_enablenoti(uint8_t con_handle, uint16_t handle);

/**
 * @brief   BLE 主设备向固定handle值服务写数据
 * 
 * @param   con_handle                          - ble连接handle
 * @param   length                              - 数据长度
 * @param   buff                                - 数据buff
 * @param   handle                              - handle值
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_master_gatt_master_write_data_withhandle(uint8_t con_handle, int length, unsigned char *buff, uint16_t handle);

/**
 * @brief   BLE 清除所有绑定信息
 * 
 * @param   空
 * 
 * @return  wlt_ble_status_t
 */
wlt_ble_status_t wlt_ble_clear_all_bond(void);
#endif


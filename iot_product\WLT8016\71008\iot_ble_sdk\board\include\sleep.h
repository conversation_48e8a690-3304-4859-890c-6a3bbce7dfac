#ifndef __SLEEP_H__
#define __SLEEP_H__

#include "sys_utils.h"

enum wakeup_io_mode
{
    WAKEUPIO_PULL_UP,
    WAKEUPIO_NOT_PULL_UP,
};

enum powerdown_value
{
    POWERDOWN_VALUE_A,
    POWERDOWN_VALUE_B,
};

/**
 * @brief   唤醒 IO 初始化
 * 
 * @param   pin                     - 唤醒IO口
 * @param   mode                    - 唤醒IO口模式
 * 
 * @return  空
 */
void dev_wakeup_io_init(uint16_t pin, enum wakeup_io_mode mode);

/**
 * @brief   进入SLEEP模式
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_into_sleep(void);

/**
 * @brief   退出SLEEP模式
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_exti_sleep(void);

/**
 * @brief   进入POWERDOWN模式
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_into_powerdown(void);

/**
 * @brief   设置在进入powerdown模式后仍然能够保存的值
 * 
 * @param   mode                     - 模式 参考：powerdown_value
 * @param   value                    - 值
 * 
 * @return  空
 */
void dev_powerdown_set_value(enum powerdown_value mode,unsigned int value);

/**
 * @brief   获取在进入powerdown模式前保存的值
 * 
 * @param   mode                     - 模式 参考：powerdown_value
 * 
 * @return  值
 */
unsigned int dev_powerdown_get_value(enum powerdown_value mode);
#endif /* __SLEEP_H__ */

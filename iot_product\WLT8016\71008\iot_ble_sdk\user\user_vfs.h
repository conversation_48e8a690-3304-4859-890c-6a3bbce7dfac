#ifndef __USER_VFS_H__
#define __USER_VFS_H__

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "user_dev.h"


#ifdef __cplusplus
extern "C" {
#endif

int open(dev_t* dev);
int close(dev_t* dev);
int read(dev_t* dev, uint8_t* data);
int write(dev_t* dev, uint8_t* data, int len);
int ioctl(dev_t* dev, int cmd, uint8_t *data);

#ifdef __cplusplus
}
#endif

#endif /* __USER_VFS_H__ */

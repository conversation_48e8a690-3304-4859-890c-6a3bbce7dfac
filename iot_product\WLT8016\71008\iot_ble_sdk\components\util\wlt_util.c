/**
 * Copyright (c) 2021, wi-linktech
 * 
 * All rights reserved.
 * 
 * 
 */

#include "wlt_util.h"

#include <stdio.h>
#include <string.h>

uint16_t wlt_little_endian_read_16(const uint8_t *buffer, int pos)
{
    return (uint16_t)(((uint16_t)buffer[pos]) | (((uint16_t)buffer[(pos) + 1]) << 8));
}
uint32_t wlt_little_endian_read_24(const uint8_t *buffer, int pos)
{
    return ((uint32_t)buffer[pos]) | (((uint32_t)buffer[(pos) + 1]) << 8) | (((uint32_t)buffer[(pos) + 2]) << 16);
}
uint32_t wlt_little_endian_read_32(const uint8_t *buffer, int pos)
{
    return ((uint32_t)buffer[pos]) | (((uint32_t)buffer[(pos) + 1]) << 8) | (((uint32_t)buffer[(pos) + 2]) << 16) | (((uint32_t)buffer[(pos) + 3]) << 24);
}

void wlt_little_endian_store_16(uint8_t *buffer, uint16_t pos, uint16_t value)
{
    buffer[pos++] = (uint8_t)value;
    buffer[pos++] = (uint8_t)(value >> 8);
}

void wlt_little_endian_store_24(uint8_t *buffer, uint16_t pos, uint32_t value)
{
    buffer[pos++] = (uint8_t)(value);
    buffer[pos++] = (uint8_t)(value >> 8);
    buffer[pos++] = (uint8_t)(value >> 16);
}

void wlt_little_endian_store_32(uint8_t *buffer, uint16_t pos, uint32_t value)
{
    buffer[pos++] = (uint8_t)(value);
    buffer[pos++] = (uint8_t)(value >> 8);
    buffer[pos++] = (uint8_t)(value >> 16);
    buffer[pos++] = (uint8_t)(value >> 24);
}

uint32_t wlt_big_endian_read_16(const uint8_t *buffer, int pos)
{
    return (uint16_t)(((uint16_t)buffer[(pos) + 1]) | (((uint16_t)buffer[pos]) << 8));
}

uint32_t wlt_big_endian_read_24(const uint8_t *buffer, int pos)
{
    return (((uint32_t)buffer[(pos) + 2]) | (((uint32_t)buffer[(pos) + 1]) << 8) | (((uint32_t)buffer[pos]) << 16));
}

uint32_t wlt_big_endian_read_32(const uint8_t *buffer, int pos)
{
    return ((uint32_t)buffer[(pos) + 3]) | (((uint32_t)buffer[(pos) + 2]) << 8) | (((uint32_t)buffer[(pos) + 1]) << 16) | (((uint32_t)buffer[pos]) << 24);
}

void wlt_big_endian_store_16(uint8_t *buffer, uint16_t pos, uint16_t value)
{
    buffer[pos++] = (uint8_t)(value >> 8);
    buffer[pos++] = (uint8_t)(value);
}

void wlt_big_endian_store_24(uint8_t *buffer, uint16_t pos, uint32_t value)
{
    buffer[pos++] = (uint8_t)(value >> 16);
    buffer[pos++] = (uint8_t)(value >> 8);
    buffer[pos++] = (uint8_t)(value);
}

void wlt_big_endian_store_32(uint8_t *buffer, uint16_t pos, uint32_t value)
{
    buffer[pos++] = (uint8_t)(value >> 24);
    buffer[pos++] = (uint8_t)(value >> 16);
    buffer[pos++] = (uint8_t)(value >> 8);
    buffer[pos++] = (uint8_t)(value);
}

// general swap/endianess utils
void wlt_reverse_bytes(const uint8_t *src, uint8_t *dst, int len)
{
    int i;
    for (i = 0; i < len; i++)
        dst[len - 1 - i] = src[i];
}
void wlt_reverse_24(const uint8_t *src, uint8_t *dst)
{
    wlt_reverse_bytes(src, dst, 3);
}
void wlt_reverse_48(const uint8_t *src, uint8_t *dst)
{
    wlt_reverse_bytes(src, dst, 6);
}
void wlt_reverse_56(const uint8_t *src, uint8_t *dst)
{
    wlt_reverse_bytes(src, dst, 7);
}
void wlt_reverse_64(const uint8_t *src, uint8_t *dst)
{
    wlt_reverse_bytes(src, dst, 8);
}
void wlt_reverse_128(const uint8_t *src, uint8_t *dst)
{
    wlt_reverse_bytes(src, dst, 16);
}
void wlt_reverse_256(const uint8_t *src, uint8_t *dst)
{
    wlt_reverse_bytes(src, dst, 32);
}

//void wlt_reverse_bd_addr(const bd_addr_t src, bd_addr_t dest)
//{
//    wlt_reverse_bytes(src, dest, 6);
//}

uint32_t wlt_min(uint32_t a, uint32_t b)
{
    return a < b ? a : b;
}

uint32_t wlt_max(uint32_t a, uint32_t b)
{
    return a > b ? a : b;
}

/**
 * @brief Calculate delta between two points in time
 * @returns time_a - time_b - result > 0 if time_a is newer than time_b
 */
int32_t wlt_time_delta(uint32_t time_a, uint32_t time_b)
{
    return (int32_t)(time_a - time_b);
}

static const char *char_to_nibble = "0123456789ABCDEF";

char wlt_char_for_nibble(int nibble)
{
    if (nibble < 16)
    {
        return char_to_nibble[nibble];
    }
    else
    {
        return '?';
    }
}

//static inline char char_for_high_nibble(int value){
//    return wlt_char_for_nibble((value >> 4) & 0x0f);
//}

//static inline char char_for_low_nibble(int value){
//    return wlt_char_for_nibble(value & 0x0f);
//}

int wlt_nibble_for_char(char c)
{
    if (c >= '0' && c <= '9')
        return c - '0';
    if (c >= 'a' && c <= 'f')
        return c - 'a' + 10;
    if (c >= 'A' && c <= 'F')
        return c - 'A' + 10;
    return -1;
}

uint32_t wlt_atoi(const char *str)
{
    uint32_t val = 0;
    while (1)
    {
        char chr = *str;
        if (!chr || chr < '0' || chr > '9')
            return val;
        val = (val * 10) + (uint8_t)(chr - '0');
        str++;
    }
}

int wlt_string_len_for_uint32(uint32_t i)
{
    if (i < 10)
        return 1;
    if (i < 100)
        return 2;
    if (i < 1000)
        return 3;
    if (i < 10000)
        return 4;
    if (i < 100000)
        return 5;
    if (i < 1000000)
        return 6;
    if (i < 10000000)
        return 7;
    if (i < 100000000)
        return 8;
    if (i < 1000000000)
        return 9;
    return 10;
}

int wlt_count_set_bits_uint32(uint32_t x)
{
    x = (x & 0x55555555) + ((x >> 1) & 0x55555555);
    x = (x & 0x33333333) + ((x >> 2) & 0x33333333);
    x = (x & 0x0F0F0F0F) + ((x >> 4) & 0x0F0F0F0F);
    x = (x & 0x00FF00FF) + ((x >> 8) & 0x00FF00FF);
    x = (x & 0x0000FFFF) + ((x >> 16) & 0x0000FFFF);
    return x;
}

/*  
 * CRC (reversed crc) lookup table as calculated by the table generator in ETSI TS 101 369 V6.3.0.
 */

#define CRC8_INIT 0xFF // Initial FCS value
#define CRC8_OK 0xCF   // Good final FCS value

static const uint8_t crc8table[256] = {/* reversed, 8-bit, poly=0x07 */
                                       0x00, 0x91, 0xE3, 0x72, 0x07, 0x96, 0xE4, 0x75, 0x0E, 0x9F, 0xED, 0x7C, 0x09, 0x98, 0xEA, 0x7B,
                                       0x1C, 0x8D, 0xFF, 0x6E, 0x1B, 0x8A, 0xF8, 0x69, 0x12, 0x83, 0xF1, 0x60, 0x15, 0x84, 0xF6, 0x67,
                                       0x38, 0xA9, 0xDB, 0x4A, 0x3F, 0xAE, 0xDC, 0x4D, 0x36, 0xA7, 0xD5, 0x44, 0x31, 0xA0, 0xD2, 0x43,
                                       0x24, 0xB5, 0xC7, 0x56, 0x23, 0xB2, 0xC0, 0x51, 0x2A, 0xBB, 0xC9, 0x58, 0x2D, 0xBC, 0xCE, 0x5F,
                                       0x70, 0xE1, 0x93, 0x02, 0x77, 0xE6, 0x94, 0x05, 0x7E, 0xEF, 0x9D, 0x0C, 0x79, 0xE8, 0x9A, 0x0B,
                                       0x6C, 0xFD, 0x8F, 0x1E, 0x6B, 0xFA, 0x88, 0x19, 0x62, 0xF3, 0x81, 0x10, 0x65, 0xF4, 0x86, 0x17,
                                       0x48, 0xD9, 0xAB, 0x3A, 0x4F, 0xDE, 0xAC, 0x3D, 0x46, 0xD7, 0xA5, 0x34, 0x41, 0xD0, 0xA2, 0x33,
                                       0x54, 0xC5, 0xB7, 0x26, 0x53, 0xC2, 0xB0, 0x21, 0x5A, 0xCB, 0xB9, 0x28, 0x5D, 0xCC, 0xBE, 0x2F,
                                       0xE0, 0x71, 0x03, 0x92, 0xE7, 0x76, 0x04, 0x95, 0xEE, 0x7F, 0x0D, 0x9C, 0xE9, 0x78, 0x0A, 0x9B,
                                       0xFC, 0x6D, 0x1F, 0x8E, 0xFB, 0x6A, 0x18, 0x89, 0xF2, 0x63, 0x11, 0x80, 0xF5, 0x64, 0x16, 0x87,
                                       0xD8, 0x49, 0x3B, 0xAA, 0xDF, 0x4E, 0x3C, 0xAD, 0xD6, 0x47, 0x35, 0xA4, 0xD1, 0x40, 0x32, 0xA3,
                                       0xC4, 0x55, 0x27, 0xB6, 0xC3, 0x52, 0x20, 0xB1, 0xCA, 0x5B, 0x29, 0xB8, 0xCD, 0x5C, 0x2E, 0xBF,
                                       0x90, 0x01, 0x73, 0xE2, 0x97, 0x06, 0x74, 0xE5, 0x9E, 0x0F, 0x7D, 0xEC, 0x99, 0x08, 0x7A, 0xEB,
                                       0x8C, 0x1D, 0x6F, 0xFE, 0x8B, 0x1A, 0x68, 0xF9, 0x82, 0x13, 0x61, 0xF0, 0x85, 0x14, 0x66, 0xF7,
                                       0xA8, 0x39, 0x4B, 0xDA, 0xAF, 0x3E, 0x4C, 0xDD, 0xA6, 0x37, 0x45, 0xD4, 0xA1, 0x30, 0x42, 0xD3,
                                       0xB4, 0x25, 0x57, 0xC6, 0xB3, 0x22, 0x50, 0xC1, 0xBA, 0x2B, 0x59, 0xC8, 0xBD, 0x2C, 0x5E, 0xCF};

/*-----------------------------------------------------------------------------------*/
static uint8_t crc8(uint8_t *data, uint16_t len)
{
    uint16_t count;
    uint8_t crc = CRC8_INIT;
    for (count = 0; count < len; count++)
    {
        crc = crc8table[crc ^ data[count]];
    }
    return crc;
}

/*-----------------------------------------------------------------------------------*/
uint8_t wlt_crc8_check(uint8_t *data, uint16_t len, uint8_t check_sum)
{
    uint8_t crc;
    crc = crc8(data, len);
    crc = crc8table[crc ^ check_sum];
    if (crc == CRC8_OK)
    {
        return 0; /* Valid */
    }
    else
    {
        return 1; /* Failed */
    }
}

/*-----------------------------------------------------------------------------------*/
uint8_t wlt_crc8_calc(uint8_t *data, uint16_t len)
{
    /* Ones complement */
    return 0xFF - crc8(data, len);
}

/* The following function is responsible for converting data of type */
/* BD_ADDR to a string.  The first parameter of this function is the */
/* BD_ADDR to be converted to a string.  The second parameter of this*/
/* function is a pointer to the string in which the converted BD_ADDR*/
/* is to be stored.                                                  */
void bd_addr_to_str(bd_addr_t Board_Address, mac_str_t BoardStr)
{
    sprintf((char *)BoardStr, "0x%02X%02X%02X%02X%02X%02X", Board_Address.bd_addr5, Board_Address.bd_addr4, Board_Address.bd_addr3, Board_Address.bd_addr2, Board_Address.bd_addr1, Board_Address.bd_addr0);
}

/* The following function is responsible for the specified string    */
/* into data of type BD_ADDR.  The first parameter of this function  */
/* is the BD_ADDR string to be converted to a BD_ADDR.  The second   */
/* parameter of this function is a pointer to the BD_ADDR in which   */
/* the converted BD_ADDR String is to be stored.                     */
void str_to_bd_addr(char *BoardStr, bd_addr_t *Board_Address)
{
    char *TempPtr;
    unsigned int StringLength;
    unsigned int Index;
    unsigned char Value;

    if ((BoardStr) && ((StringLength = strlen(BoardStr)) >= (sizeof(bd_addr_t) * 2)) && (Board_Address))
    {
        TempPtr = BoardStr;
        if ((StringLength >= (sizeof(bd_addr_t) * 2) + 2) && (TempPtr[0] == '0') && ((TempPtr[1] == 'x') || (TempPtr[1] == 'X')))
            TempPtr += 2;

        for (Index = 0; Index < 6; Index++)
        {
            Value = (char)(ToInt(*TempPtr) * 0x10);
            TempPtr++;
            Value += (char)ToInt(*TempPtr);
            TempPtr++;
            ((char *)Board_Address)[5 - Index] = (uint8_t)Value;
        }
    }
    else
    {
        if (Board_Address)
            memset(Board_Address, 0, sizeof(bd_addr_t));
    }
}

// void BD_ADDR_Change_A(BD_ADDR_t *BD_ADDR, bd_addr_t bd_addr)
// {
//     BD_ADDR->BD_ADDR0 = bd_addr[0];
//     BD_ADDR->BD_ADDR1 = bd_addr[1];
//     BD_ADDR->BD_ADDR2 = bd_addr[2];
//     BD_ADDR->BD_ADDR3 = bd_addr[3];
//     BD_ADDR->BD_ADDR4 = bd_addr[4];
//     BD_ADDR->BD_ADDR5 = bd_addr[5];
// }

// void BD_ADDR_Change_B(bd_addr_t bd_addr, BD_ADDR_t *BD_ADDR)
// {
//     bd_addr[0] = BD_ADDR->BD_ADDR5;
//     bd_addr[1] = BD_ADDR->BD_ADDR4;
//     bd_addr[2] = BD_ADDR->BD_ADDR3;
//     bd_addr[3] = BD_ADDR->BD_ADDR2;
//     bd_addr[4] = BD_ADDR->BD_ADDR1;
//     bd_addr[5] = BD_ADDR->BD_ADDR0;
// }

//
char char_for_nibble(int nibble){

    static const char * char_to_nibble = "0123456789ABCDEF";

    if (nibble < 16){
        return char_to_nibble[nibble];
    } else {
        return '?';
    }
}

static inline char char_for_high_nibble(int value){
    return char_for_nibble((value >> 4) & 0x0f);
}

static inline char char_for_low_nibble(int value){
    return char_for_nibble(value & 0x0f);
}

//static char bd_addr_to_str_buffer[6*3];  // 12:45:78:01:34:67\0
static unsigned char bd_addr_to_str_buffer[15];  // 0xC53834313049\0
unsigned char * bd_address_to_str(bd_addr_t addr){
    unsigned char * p = bd_addr_to_str_buffer;
    int i;
		*p++ = '0';
		*p++ = 'x';
    for (i = 0; i < 6 ; i++) {
				uint8_t byte = 0;
				switch(i)
				{
					case 0:
						byte = addr.bd_addr5;
						break;
					case 1:
						byte = addr.bd_addr4;
						break;
					case 2:
						byte = addr.bd_addr3;
						break;
					case 3:
						byte = addr.bd_addr2;
						break;
					case 4:
						byte = addr.bd_addr1;
						break;
					case 5:
						byte = addr.bd_addr0;
						break;
				}
        *p++ = char_for_high_nibble(byte);
        *p++ = char_for_low_nibble(byte);
    }
    *p = 0;
    return (unsigned char *) bd_addr_to_str_buffer;
}
//void bd_address_to_str(bd_addr_t addr, unsigned char *mac){
//    char * p = bd_addr_to_str_buffer;
//    int i;
//		*p++ = '0';
//		*p++ = 'x';
//    for (i = 0; i < 6 ; i++) {
//				uint8_t byte = 0;
//				switch(i)
//				{
//					case 0:
//						byte = addr.bd_addr5;
//						break;
//					case 1:
//						byte = addr.bd_addr4;
//						break;
//					case 2:
//						byte = addr.bd_addr3;
//						break;
//					case 3:
//						byte = addr.bd_addr2;
//						break;
//					case 4:
//						byte = addr.bd_addr1;
//						break;
//					case 5:
//						byte = addr.bd_addr0;
//						break;
//				}
//        *p++ = char_for_high_nibble(byte);
//        *p++ = char_for_low_nibble(byte);
//    }
//    *p = 0;
//    memcpy(mac, bd_addr_to_str_buffer, 15);
//}
//

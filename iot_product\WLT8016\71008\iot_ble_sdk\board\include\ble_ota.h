#ifndef __BLE_OTA_H__
#define __BLE_OTA_H__
#include "wlt_util.h"
#include "wlt_ble_type.h"

/**
 * @brief   BLE OTA 判断是否可以读数据
 * 
 * @param   空
 * 
 * @return  0：不能读取
 *          1：可以读取
 */
unsigned char ble_ota_get_canread_flag(void);

/**
 * @brief   BLE OTA 读数据
 * 
 * @param   data              - 读数据buffer
 * 
 * @return  读数据长度
 */
unsigned char ble_ota_read_data(unsigned char *data);

/**
 * @brief   BLE OTA 写数据
 * 
 * @param   con_handle          - ble连接handle
 *          p_data              - 写数据buffer
 *          len                 - 写数据长度
 * 
 * @return  空
 */
void ble_ota_write_data(uint16_t con_handle,uint8_t *p_data,uint16_t len);
#endif


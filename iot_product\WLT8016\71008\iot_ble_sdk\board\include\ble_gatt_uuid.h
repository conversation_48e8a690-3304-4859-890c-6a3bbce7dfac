#ifndef _BLE_GATT_UUID_H
#define _BLE_GATT_UUID_H
#include <stdio.h>
#include <stdint.h>

#define BLE_UUID_SIZE_2         2                   //!< 2 bytes UUID, usually SIG assigned UUID.
#define BLE_UUID_SIZE_16        16                  //!< 16 bytes UUID, usually assigned by users. 

/** @defgroup GATT_PROP_BITMAPS_DEFINES GATT Attribute Access Permissions Bit Fields
 * @{
 */
#define BLE_GATT_PROP_BROADCAST             (1<<0)  //!< Attribute is able to broadcast
#define BLE_GATT_PROP_READ                  (1<<1)  //!< Attribute is Readable
#define BLE_GATT_PROP_WRITE_CMD             (1<<2)  //!< Attribute supports write with no response
#define BLE_GATT_PROP_WRITE_REQ             (1<<3)  //!< Attribute supports write request
#define BLE_GATT_PROP_NOTI                  (1<<4)  //!< Attribute is able to send notification
#define BLE_GATT_PROP_INDI                  (1<<5)  //!< Attribute is able to send indication
#define BLE_GATT_PROP_AUTH_SIG_WRTIE        (1<<6)  //!< Attribute supports authenticated signed write
#define BLE_GATT_PROP_EXTEND_PROP           (1<<7)  //!< Attribute supports extended properities
#define BLE_GATT_PROP_WRITE                 (1<<8)  //!< Attribute is Writable (support both write_req and write_cmd)
#define BLE_GATT_PROP_AUTHEN                (1<<9)  //!< Attribute requires Authentication

/*********************************************************************
 * CONSTANTS
 */
 // SIG UUID size is always 2.
#define BLE_ATT_BT_UUID_SIZE 2

/*
 * WARNING: The 16-bit UUIDs are assigned by the Bluetooth SIG and published
 *          in the Bluetooth Assigned Numbers page. Do not change these values.
 *          Changing them will cause Bluetooth interoperability issues.
 */

/**
 * GATT Services
 */
#define BLE_GAP_SERVICE_UUID                           0x1800 // Generic Access Profile
#define BLE_GATT_SERVICE_UUID                          0x1801 // Generic Attribute Profile

/**
 * GATT Declarations
 */
#define BLE_GATT_PRIMARY_SERVICE_UUID                  0x2800 // Primary Service
#define BLE_GATT_SECONDARY_SERVICE_UUID                0x2801 // Secondary Service
#define BLE_GATT_INCLUDE_UUID                          0x2802 // Include
#define BLE_GATT_CHARACTER_UUID                        0x2803 // Characteristic

/**
 * GATT Descriptors
 */
#define BLE_GATT_CHAR_EXT_PROPS_UUID                   0x2900 // Characteristic Extended Properties
#define BLE_GATT_CHAR_USER_DESC_UUID                   0x2901 // Characteristic User Description
#define BLE_GATT_CLIENT_CHAR_CFG_UUID                  0x2902 // Client Characteristic Configuration
#define BLE_GATT_SERV_CHAR_CFG_UUID                    0x2903 // Server Characteristic Configuration
#define BLE_GATT_CHAR_FORMAT_UUID                      0x2904 // Characteristic Presentation Format
#define BLE_GATT_CHAR_AGG_FORMAT_UUID                  0x2905 // Characteristic Aggregate Format
#define BLE_GATT_VALID_RANGE_UUID                      0x2906 // Valid Range
#define BLE_GATT_EXT_REPORT_REF_UUID                   0x2907 // External Report Reference Descriptor
#define BLE_GATT_REPORT_REF_UUID                       0x2908 // Report Reference Descriptor

/**
 * GATT Characteristics
 */
#define BLE_GATT_DEVICE_NAME_UUID                           0x2A00 // Device Name
#define BLE_GATT_APPEARANCE_UUID                            0x2A01 // Appearance
#define BLE_GATT_PERI_PRIVACY_FLAG_UUID                     0x2A02 // Peripheral Privacy Flag
#define BLE_GATT_RECONNECT_ADDR_UUID                        0x2A03 // Reconnection Address
#define BLE_GATT_PERI_CONN_PARAM_UUID                       0x2A04 // Peripheral Preferred Connection Parameters
#define BLE_GATT_SERVICE_CHANGED_UUID                       0x2A05 // Service Changed
#define BLE_GATT_ADDR_RESOL_SUPP                            0x2AA6 // Central Address Resolution supported
#define BLE_GATT_RSLV_PRIV_ADDR_ONLY                        0x2AC9 // Resolvable Private Address only











/*********************************************************************
 * MACROS
 */

#define WLT_BUILD_UINT16(loByte, hiByte) \
          ((uint16_t)(((loByte) & 0x00FF) + (((hiByte) & 0x00FF) << 8)))

#define WLT_HI_UINT16(a) (((a) >> 8) & 0xFF)
#define WLT_LO_UINT16(a) ((a) & 0xFF)

#define WLT_UUID16_ARR(uuid16)  {uuid16&0xff,(uuid16&0xff00)>>8}
#define WLT_UUID128_ARR(uuid16)       {0xFB, 0x34, 0x9B, 0x5F, 0x80, 0x00, 0x00, 0x80, \
                                            0x00, 0x10, 0x00, 0x00, (uuid16 & 0xff),(uuid16&0xff00)>>8, 0x00, 0x00}

/*********************************************************************
 * TYPEDEFS
 */

/*********************************************************************
 * VARIABLES
 */



#endif /* GATT_SIG_UUID_H */


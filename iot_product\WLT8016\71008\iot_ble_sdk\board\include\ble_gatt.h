#ifndef _BLE_GATT_H
#define _BLE_GATT_H
#include <stdint.h>

typedef struct
{
  uint8_t len;         //!< Length of UUID
  uint8_t uuid[16]; //!< Pointer to UUID
}ble_gatt_attr_type_t;

/**
 * GATT Attribute format.
 */
typedef struct ble_gatt_attribute
{
  ble_gatt_attr_type_t type; //!< Attribute type (2 or 16 octet UUIDs)
  uint16_t permissions;   //!< Attribute permissions
  uint16_t max_size;       //!< Attribute handle - assigned internally by attribute server
  uint8_t* pValue; //!< Attribute value - encoding of the octet array is defined in 
                        //!< the applicable profile. The maximum length of an attribute 
                        //!< value shall be 512 octets.
}ble_gatt_attribute_t;

typedef struct {
  uint16_t (*gatt_read_callback)(uint16_t con_handle, uint16_t attribute_handle, uint8_t * buffer,uint16_t buffer_size);
  int (*gatt_write_callback)(uint16_t con_handle, uint16_t attribute_handle, uint8_t *buffer, uint16_t buffer_size);
  //void (*packet_handler) (uint8_t packet_type, uint16_t channel, uint8_t *packet, uint16_t size);
  uint16_t size_db;
  const ble_gatt_attribute_t *ble_device_db;
}blestack_init_t;

typedef struct {
  uint16_t (*gatt_master_notification_callback)(uint16_t con_handle, uint16_t attribute_handle, uint8_t * buffer,uint16_t buffer_size);
  uint16_t (*gatt_master_read_callback)(uint16_t con_handle, uint16_t attribute_handle, uint8_t * buffer,uint16_t buffer_size);
	
	const ble_gatt_attr_type_t *ble_master_db;
}blestack_master_init_t;

/**
 * @brief   BLE 服务初始化
 * 
 * @param   init        - blestack_init_t 类型结构体变量
 * 
 * @return  空
 */
int ble_gatt_init(void *init);

/**
 * @brief   BLE 添加HID服务
 * 
 * @param   空
 * 
 * @return  空
 */
void ble_add_hid_service(void);
#endif

#ifndef __I2C_H__
#define __I2C_H__

#include "sys_utils.h"

enum iic_channel
{
    CHANNEL_0,
    CHANNEL_1,
    CHANNEL_MAX,
};

/**
 * @brief   IIC IO 初始化
 * 
 * @param   clk_pin                 - 时钟引脚
 * @param   dat_pin                 - 数据引脚
 * 
 * @return  空
 */
void dev_iic_io_init(uint16_t clk_pin, uint16_t dat_pin);

/**
 * @brief   IIC 写一个字节
 * 
 * @param   channel                 - iic channel：CHANNEL_0/CHANNEL_1
 * @param   slave_addr              - 从机地址
 * @param   reg_addr                - 寄存器地址
 * @param   data                    - 数据
 * 
 * @return  空
 */
uint8_t dev_iic_write_byte(enum iic_channel channel, uint8_t slave_addr, uint8_t reg_addr, uint8_t data);

/**
 * @brief   IIC 写多个字节
 * 
 * @param   channel                 - iic channel：CHANNEL_0/CHANNEL_1
 * @param   slave_addr              - 从机地址
 * @param   reg_addr                - 寄存器地址
 * @param   buffer                  - 发送数据buffer首地址
 * @param   length                  - 发送数据长度
 * 
 * @return  空
 */
uint8_t dev_iic_write_bytes(enum iic_channel channel, uint8_t slave_addr, uint8_t reg_addr, uint8_t *buffer, uint16_t length);

/**
 * @brief   IIC 读一个字节
 * 
 * @param   channel                 - iic channel：CHANNEL_0/CHANNEL_1
 * @param   slave_addr              - 从机地址
 * @param   reg_addr                - 寄存器地址
 * @param   buffer                  - 读取数据buffer首地址
 * 
 * @return  空
 */
uint8_t dev_iic_read_byte(enum iic_channel channel, uint8_t slave_addr, uint8_t reg_addr, uint8_t *buffer);

/**
 * @brief   IIC 写多个字节
 * 
 * @param   channel                 - iic channel：CHANNEL_0/CHANNEL_1
 * @param   slave_addr              - 从机地址
 * @param   reg_addr                - 寄存器地址
 * @param   buffer                  - 读取数据buffer首地址
 * @param   length                  - 读取数据长度
 * 
 * @return  空
 */
uint8_t dev_iic_read_bytes(enum iic_channel channel, uint8_t slave_addr, uint8_t reg_addr, uint8_t *buffer, uint16_t length);

/**
 * @brief   IIC 初始化
 * 
 * @param   channel                 - iic channel：CHANNEL_0/CHANNEL_1
 * @param   speed                   - iic 速率：速度 = speed * 1000
 * @param   slave_addr              - 作为从机时的地址
 * 
 * @return  空
 */
void dev_iic_init(enum iic_channel channel, uint16_t speed, uint16_t slave_addr);
#endif /* __I2C_H__ */


#ifndef __RTC_H__
#define __RTC_H__

#include "sys_utils.h"

#define GET_RTC_INTERVAL              1   //单位秒
#define GET_RTC_INTERVAL_SLEEP        20   //单位秒,0为关闭

typedef struct rtc_clock
{
    uint16_t year;
    uint16_t month;
    uint16_t day;
    uint16_t week;
    uint16_t hour;
    uint16_t min;
    uint16_t sec;
} rtc_clock_t;

/**
 * @brief   启动RTC
 * 
 * @param   rtc_time               - RTC计时时间 单位：秒 范围：0 - 20 注意：如果设置进入休眠模式，芯片会根据rtc_time间隔唤醒，如果要降低功耗，可以增大RTC计时时间
 * 
 * @return  空
 */
void dev_rtc_start(unsigned int rtc_time);

/**
 * @brief   获取RTC当前时间
 * 
 * @param   time                    - rtc_clock_t 结构体变量 注意：初始时间从2023.1.1开始计时
 * 
 * @return  空
 */
void dev_rtc_get_time(rtc_clock_t *time);
#endif /* __RTC_H__ */


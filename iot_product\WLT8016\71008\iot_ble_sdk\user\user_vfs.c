#include "user_vfs.h"

int open(dev_t* dev)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    
    if(dev->ops->open)
    {
        ret = dev->ops->open(dev);
    }
    
    return ret;
}

int close(dev_t* dev)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    
    if(dev->ops->close)
    {
        ret = dev->ops->close(dev);
    }
    
    return ret;
}

int read(dev_t* dev, uint8_t* data)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    
    if(dev->ops->read)
    {
        ret = dev->ops->read(dev,data);
    }
    
    return ret;
}

int write(dev_t* dev, uint8_t* data, int len)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    
    if(dev->ops->write)
    {
        ret = dev->ops->write(dev,data,len);
    }
    
    return ret;
}

int ioctl(dev_t* dev, int cmd, uint8_t *data)
{
    int ret = 0;
    if(dev == NULL){
        return -1;
    }
    
    if(dev->ops->ioctl)
    {
        ret = dev->ops->ioctl(dev,cmd,data);
    }
    
    return ret;
}





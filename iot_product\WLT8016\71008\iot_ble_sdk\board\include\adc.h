#ifndef __ADC_H__
#define __ADC_H__

#include "sys_utils.h"
#include <stdbool.h>
#include "gpio.h"

#define ADC_CHANNEL_0   IO_PD4
#define ADC_CHANNEL_1   IO_PD5
#define ADC_CHANNEL_2   IO_PD6
#define ADC_CHANNEL_3   IO_PD7

enum adc_reference_cfg_t {
    ADC_REFERENCE_CFG_INTERNAL = 0x00,
    ADC_REFERENCE_CFG_AVDD = 0x01,
};

typedef void (*adc_highprec_callback)(int total_value,uint16_t sample_count);

/**
 * @brief   ADC 初始化
 * 
 * @param   channels                - ADC channel
 * @param   ref                     - 参考电压源
 * 
 * @return  空
 */
void dev_adc_init(uint8_t channels,enum adc_reference_cfg_t ref);

/**
 * @brief   ADC 开启
 * 
 * @param   空
 * 
 * @return  true/false
 */
bool dev_adc_enable(void);

/**
 * @brief   ADC 关闭
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_adc_disable(void);

/**
 * @brief   ADC 读取引脚电压
 * 
 * @param   channels                - ADC channel
 * @param   buffer                  - 读取电压值存放buffer首地址
 * 
 * @return  空
 */
void dev_adc_get_result(uint8_t channels, uint16_t *buffer);

/**
 * @brief   ADC 获取参考电压
 * 
 * @param   ref
 * 
 * @return  参考电压值
 */
uint16_t dev_adc_get_ref_voltage(enum adc_reference_cfg_t ref);

/**
 * @brief   读取电池电压初始化
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_adc_vbat_init(void);

/**
 * @brief   读取电池电压
 * 
 * @param   空
 * 
 * @return  电池电压值
 */
uint16_t dev_adc_vbat_get_result(void);
#endif /* __ADC_H__ */

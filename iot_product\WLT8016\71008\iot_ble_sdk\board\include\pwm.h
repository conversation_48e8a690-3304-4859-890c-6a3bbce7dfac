#ifndef __PWM_H__
#define __PWM_H__

#include "sys_utils.h"
#include <stdbool.h>

enum pwm_channel {
    PWMCHANNEL_0,
    PWMCHANNEL_1,
    PWMCHANNEL_2,
    PWMCHANNEL_3,
    PWMCHANNEL_4,
    PWMCHANNEL_5,
    PWMCHANNEL_MAX,
};

/**
 * @brief   PWM IO 初始化
 * 
 * @param   pwm_pin                 - PWM引脚
 * 
 * @return  空
 */
void dev_pwm_io_init(uint16_t pwm_pin);

/**
 * @brief   PWM 初始化
 * 
 * @param   channel                 - PWM 通道
 * @param   frequency               - PWM 频率
 * @param   total_duty              - PWM 总的分辨率
 * @param   high_duty               - PWM 高电平百分比(0-total_duty)
 * 
 * @return  空
 */
void dev_pwm_init(enum pwm_channel channel, uint32_t frequency, uint32_t total_duty, uint32_t high_duty);

/**
 * @brief   PWM 启动
 * 
 * @param   channel                 - PWM 通道
 * 
 * @return  空
 */
void dev_pwm_start(enum pwm_channel channel);

/**
 * @brief   PWM 停止
 * 
 * @param   channel                 - PWM 通道
 * 
 * @return  空
 */
void dev_pwm_stop(enum pwm_channel channel);

/**
 * @brief   PWM 更新
 * 
 * @param   channel                 - PWM 通道
 * @param   frequency               - PWM 频率
 * @param   total_duty              - PWM 总的分辨率
 * @param   high_duty               - PWM 高电平百分比(0-total_duty)
 * 
 * @return  空
 */
void dev_pwm_update(enum pwm_channel channel, uint32_t frequency, uint32_t total_duty, uint32_t high_duty);
#endif /* __PWM_H__ */


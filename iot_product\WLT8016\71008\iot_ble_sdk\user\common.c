/*
** 本文件包含重要组件函数，必须完整实现。
**
*/
#include "common.h"
#include "wlt_krnl_api.h"
#include "timer.h"

wq_data_t wq_data;
#define WQ_TICK_UNIT    (1000) //us, 1ms
#define WQ_USE_TIMER    TIMER_1

//CRC-16（因子0x1021)/XMODEM,校验多项式： X^16+X^12+X^5+1
static const uint16_t CRC16_CCITT_Tab[] =
{
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
    0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
    0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
    0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
    0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
    0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
    0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
    0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
    0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
    0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
    0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
    0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
    0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
    0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
    0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
    0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
    0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
    0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
    0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
    0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
    0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
    0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
};

/**************************************************************
* Message Queue
**************************************************************/
static MQ_HANDLE_T mq_handle = NULL;
MQ_HANDLE_T init_mq(uint16_t nums)
{
    if(mq_handle != NULL){
        return mq_handle;
    }
    mq_handle = wlt_create_mailbox(nums, sizeof(mq_msg_t));
    return mq_handle;
}

int destory_mq(void)
{
    wlt_delete_mailbox(mq_handle, NULL);
    return 0;
}

int init_msg(mq_msg_t* msg, uint8_t *data, uint16_t len)
{
    if(msg == NULL)
        return -1;
    if(len > MQ_MSG_SIZE){
        IOT_LOG_E("len is too long. max=%d, now=%d.",MQ_MSG_SIZE,len);
        return -1;
    }
    msg->len = len;
    memcpy(msg->data,data,len);
    return 0;
}

int send_mq(mq_msg_t * msg)
{
    if(mq_handle == NULL)
        return -1;
    bool ret_val = wlt_add_mailbox(mq_handle, (void *)msg);
    if(!ret_val){
        IOT_LOG_E("add mq msg failed.");
        return -1;
    }
    return 0;
}

int get_mq(mq_msg_t * msg)
{
    if(mq_handle == NULL)
        return -1;
    bool ret_val = wlt_wait_mailbox(mq_handle,msg);
    if(!ret_val){
        IOT_LOG_E("get mailbox failed.");
        return -1;
    }
    return 0;
}

/* brief
* 获取mq当前是否有数据
* @return: 0>=:有mq数据; <0:无mq数据;
*/
int query_mq(void)
{
    int ret = 0;
    bool code = wlt_query_mailbox(mq_handle);
    ret = (code == true)?0:-1;
    return ret;
}

/**************************************************************
* Event Queue
**************************************************************/
static EQ_HANDLE_T event_handle = NULL;
EQ_HANDLE_T init_eq(uint16_t nums)
{
    if(event_handle != NULL){
        return event_handle;
    }
    event_handle = wlt_create_mailbox(nums, sizeof(uint8_t));
    IOT_LOG_I("event handle = %d.",event_handle);
    return event_handle;
}

int destory_eq(void)
{
    wlt_delete_mailbox(event_handle, NULL);
    return 0;
}

/* brief
* 发送event数据
* @return: 0:成功; <0:失败;
*/
int send_eq(uint8_t event)
{
    if(event_handle == NULL){
        IOT_LOG_E("event_handle is null,send failed.");
        return -1;
    }
    bool ret_val = wlt_add_mailbox(event_handle, (void *)(&event));
    if(!ret_val){
        IOT_LOG_E("add eq msg failed.");
        return -1;
    }
    IOT_LOG_I("event: %d.",event);
    return 0;
}

/* brief
* 获取event数据
* @return: 返回event值; <0:无效;
*/
int get_eq(void)
{
    uint8_t event = 0;
    if(event_handle == NULL){
        IOT_LOG_E("event_handle is null,send failed.");
        return -1;
    }
    bool ret_val = wlt_wait_mailbox(event_handle,(void *)(&event));
    if(!ret_val){
        IOT_LOG_E("get mailbox failed.");
        return -1;
    }
    return event;
}

/* brief
* 获取event当前是否有数据
* @return: 0>=:有event数据; <0:无event数据;
*/
int query_eq(void)
{
    int ret = 0;
    bool code = wlt_query_mailbox(event_handle);
    ret = (code == true)?0:-1;
    return ret;
}
/**************************************************************
* Work Queue
**************************************************************/
static void com_wq_timer_cb(void)
{
    wq_data.tick++;
}

int com_wq_init(void)
{
    memset((void*)(&wq_data),0,sizeof(wq_data_t));
    dev_timer_init(WQ_USE_TIMER,WQ_TICK_UNIT);
    dev_set_timer_callback(WQ_USE_TIMER,com_wq_timer_cb);
    dev_timer_run(WQ_USE_TIMER);
    return 0;
}

int com_wq_query(void_cb_func_t cb)
{
   for(int i = 0; i < WQ_ELEMENT_NUM; ++i){
       if(wq_data.queue[i].use != 0 && wq_data.queue[i].cb == cb){
           return 0; //当前已存在则退出
       }
   }
   return -1;
}

int com_wq_add(uint32_t time,void_cb_func_t cb,uint32_t para)
{
    for(int i = 0; i < WQ_ELEMENT_NUM; ++i){
        if(wq_data.queue[i].use == 0){
            wq_data.queue[i].time = time + wq_data.tick;
            wq_data.queue[i].period = time;
            wq_data.queue[i].cb = cb;
            wq_data.queue[i].parameter = para;
            wq_data.queue[i].use = 1;
            return 0; //add success
        }
    }
    return -1;
}

int com_wq_delete(void_cb_func_t cb)
{
    int ret = -1;
    for(int i = 0; i < WQ_ELEMENT_NUM; ++i){
        if(wq_data.queue[i].cb == cb){
            wq_data.queue[i].use = 0;
            ret = 0; //delete success; continue delete
        }
    }
    return ret;
}

void com_wq_process(void)
{
    uint32_t time_now = wq_data.tick;
    for(int i = 0; i < WQ_ELEMENT_NUM; ++i){
        if(wq_data.queue[i].use && wq_data.queue[i].time <= time_now){
            switch(wq_data.queue[i].parameter){
                case WQ_PARAMETER_ONCE: //清除当前执行的wq
                    wq_data.queue[i].use = 0;
                break;
                case WQ_PARAMETER_TWO: //清除当前执行的wq
                    wq_data.queue[i].parameter = WQ_PARAMETER_ONCE;
                    wq_data.queue[i].time = wq_data.queue[i].period + wq_data.tick;
                break;
                case WQ_PARAMETER_THREE: //清除当前执行的wq
                    wq_data.queue[i].parameter = WQ_PARAMETER_TWO;
                    wq_data.queue[i].time = wq_data.queue[i].period + wq_data.tick;
                break;
                case WQ_PARAMETER_CYCLE: //reload time
                    wq_data.queue[i].time = wq_data.queue[i].period + wq_data.tick;
                break;
                default: //default once
                    wq_data.queue[i].use = 0;
                break;
            }
            wq_data.queue[i].cb();
        }
    }
}

/**************************************************************
* CRC 
**************************************************************/
uint16_t com_crc16_xmodem(uint8_t *ptr,uint32_t len,uint16_t base)
{
    uint16_t crc = base;
    uint8_t da = 0;
    while(len--)
    {
        da=(uint16_t)crc>>8;
        crc<<=8;
        crc^=CRC16_CCITT_Tab[da^*ptr];
        ptr++;
    }
    return crc;
}

//crc16 initial value is 0
uint16_t com_crc16_xmodem_init0(uint8_t *ptr,uint32_t len)
{
    return com_crc16_xmodem(ptr,len,0);
}

/**************************************************************
* Ending tranfer 
**************************************************************/
/*
* big ending. & small ending
*/
uint32_t com_ending_32_unpack(uint8_t is_small, uint8_t *data)
{
    uint32_t ret = 0;
    if(is_small){
        ret |= ((uint32_t)(data[3]) << 24);
        ret |= ((uint32_t)(data[2]) << 16);
        ret |= ((uint32_t)(data[1]) << 8);
        ret |= ((uint32_t)(data[0]));
    }else{
        ret |= ((uint32_t)(data[0]) << 24);
        ret |= ((uint32_t)(data[1]) << 16);
        ret |= ((uint32_t)(data[2]) << 8);
        ret |= ((uint32_t)(data[3]));
    }
    return ret;
}

void com_ending_32_pack(uint8_t is_small, uint32_t source, uint8_t *data)
{
    if(is_small){
        data[3] = (source >> 24) & 0xFF;
        data[2] = (source >> 16) & 0xFF;
        data[1] = (source >> 8) & 0xFF;
        data[0] = (source) & 0xFF;
    }else{
        data[0] = (source >> 24) & 0xFF;
        data[1] = (source >> 16) & 0xFF;
        data[2] = (source >> 8) & 0xFF;
        data[3] = (source) & 0xFF;
    }
}

uint16_t com_ending_16_unpack(uint8_t is_small, uint8_t *data)
{
    uint16_t ret = 0;
    if(is_small){
        ret |= ((uint16_t)(data[1]) << 8);
        ret |= ((uint16_t)(data[0]));
    }else{
        ret |= ((uint16_t)(data[0]) << 8);
        ret |= ((uint16_t)(data[1]));
    }
    return ret;
}

void com_ending_16_pack(uint8_t is_small, uint16_t source, uint8_t *data)
{
    if(is_small){
        data[1] = (source >> 8) & 0xFF;
        data[0] = (source) & 0xFF;
    }else{
        data[0] = (source >> 8) & 0xFF;
        data[1] = (source) & 0xFF;
    }
}









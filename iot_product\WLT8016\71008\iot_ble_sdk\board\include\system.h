#ifndef __SYSTEM_H__
#define __SYSTEM_H__
#include "wlt_ble_type.h"

enum tx_power {
    CHIPTX_POWER_NEG_16dBm,
    CHIPTX_POWER_NEG_10dBm,
    CHIPTX_POWER_NEG_7dBm,
    CHIPTX_POWER_NEG_5dBm,
    CHIPTX_POWER_NEG_3dBm,
    CHIPTX_POWER_NEG_2dBm,
    CHIPTX_POWER_NEG_1dBm,
    CHIPTX_POWER_0dBm,
    CHIPTX_POWER_POS_1dBm,
    CHIPTX_POWER_POS_2dBm,
    CHIPTX_POWER_POS_3dBm,
    CHIPTX_POWER_POS_4dBm,
    CHIPTX_POWER_POS_5dBm,
    CHIPTX_POWER_POS_6dBm,
    CHIPTX_POWER_POS_7dBm,
    CHIPTX_POWER_POS_8dBm,
    CHIPTX_POWER_POS_9dBm,
    CHIPTX_POWER_POS_10dBm,
    CHIPTX_POWER_MAX,
};

enum charge_current
{
    CURRENT_18MA = 0,
    CURRENT_40MA,
    CURRENT_72MA,
    CURRENT_113MA,
    CURRENT_152MA,
    CURRENT_185MA,
};

enum charge_volatge
{
    VOLTAGE_4_10V = 0,
    VOLTAGE_4_20V,
    VOLTAGE_4_30V,
    VOLTAGE_4_40V,
};

/**
 * @brief   获取上电后系统运行计数值
 * 
 * @param   空
 * 
 * @return  上电后系统运行计数值
 */
uint32_t wlt_get_tick_count(void);

/**
 * @brief   往串口发送一个数据
 * 
 * @param   c                       - 发送数据
 * 
 * @return  空
 */
uint8_t wlt_send_char(int c);

/**
 * @brief   设置一个运行在while(1)中的loop事件
 * 
 * @param   callback                - 事件
 * 
 * @return  空
 */
void wlt_set_user_loop_callback(void (*callback)(void));

/**
 * @brief   清除运行在while(1)中的loop事件
 * 
 * @param   空
 * 
 * @return  空
 */
void wlt_clear_user_loop_callback(void);

/**
 * @brief   设置芯片蓝牙发射功率
 * 
 * @param   tx_power                - 发射功率
 * 
 * @return  空
 */
void wlt_set_tx_power(enum tx_power);

/**
 * @brief   芯片复位
 * 
 * @param   空
 * 
 * @return  空
 */
void wlt_reset(void);

/**
 * @brief   充电功能初始化
 * 
 * @param   current                 - 充电电流
 * @param   voltage                 - 充电电压
 * @param   en                      - 使能充电功能：true：使能充电功能 false：关闭充电功能
 * 
 * @return  空
 */
void charge_init(enum charge_current current,enum charge_volatge voltage,bool en);
#endif /* __SYSTEM_H__ */

#ifndef __UART_H__
#define __UART_H__

#include "sys_utils.h"

enum uart_rate
{
    BAUDRATE_1200 = 0,
    BAUDRATE_2400,
    BAUDRATE_4800,
    BAUDRATE_9600,
    BAUDRATE_14400,
    BAUDRATE_19200,
    BAUDRATE_38400,
    BAUDRATE_57600,
    BAUDRATE_115200,
    BAUDRATE_230400,
    BAUDRATE_460800,
    BAUDRATE_921600,
};

enum uart_data_bit_num
{
    DATA_BIT_NUM_5 = 0,
    DATA_BIT_NUM_6,
    DATA_BIT_NUM_7,
    DATA_BIT_NUM_8,
};

enum uart_parity
{
    NO_PARITY = 0,
    ODD_PARITY,
    EVEN_PARITY,
};

enum uart_stop_bit
{
    STOP_BIT_1 = 0,
    STOP_BIT_2,
};


#define UART0_ADDR                            0
#define UART1_ADDR                            1

typedef struct
{
    unsigned int    dev_baudrate;       //波特率
    unsigned char   dev_data_bit_num;   //数据位
    unsigned char   dev_parity;         //校验位
    unsigned char   dev_stop_bit;       //停止位
} dev_uart_para_t;

typedef void (*uart_callback)(uint8_t *data, uint8_t length);

/**
 * @brief   串口初始化函数
 * 
 * @param   uart_addr               - 串口地址：UART0_ADDR / UART1_ADDR
 * @param   txpin                   - 串口发送引脚
 * @param   rxpin                   - 串口接收引脚
 * @param   baudrate                - 串口相关参数
 * 
 * @return  空
 */
void dev_uart_init(uint32_t uart_addr, uint16_t txpin, uint16_t rxpin, dev_uart_para_t dev_uart_para);

/**
 * @brief   串口读取函数
 * 
 * @param   uart_addr               - 串口地址：UART0_ADDR / UART1_ADDR
 * @param   buf                     - 串口读取buffer首地址
 * @param   size                    - 串口读取大小
 * 
 * @return  空
 */
void dev_uart_read(uint32_t uart_addr, uint8_t *buf, uint32_t size); 

/**
 * @brief   串口发送函数
 * 
 * @param   uart_addr               - 串口地址：UART0_ADDR / UART1_ADDR
 * @param   bufptr                  - 串口发送buffer首地址
 * @param   size                    - 串口发送大小
 * 
 * @return  空
 */
void dev_uart_write(uint32_t uart_addr, const uint8_t *bufptr, uint32_t size);

/**
 * @brief   串口接收回调函数设置
 * 
 * @param   uart_addr               - 串口地址：UART0_ADDR / UART1_ADDR
 * @param   uart_callback_temp      - 回调函数
 * 
 * @return  空
 */
void dev_set_uart_callback(uint32_t uart_addr,uart_callback uart_callback_temp);
#endif /* __UART_H__ */


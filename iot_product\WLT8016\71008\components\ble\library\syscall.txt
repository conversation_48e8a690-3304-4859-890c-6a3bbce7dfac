#<SYMDEFS># ARM Linker, 5060422: Last Updated: Tue Jun 11 09:29:06 2019
0x00000000 N __ARM_use_no_argv
0x00000000 D __Vectors
0x0000008d T __main
0x0000008d T _main_stk
0x00000091 T _main_scatterload
0x00000095 T __main_after_scatterload
0x00000095 T _main_clock
0x00000095 T _main_cpp_init
0x00000095 T _main_init
0x0000009d T __rt_final_cpp
0x0000009d T __rt_final_exit
0x000000a1 T rwip_sleep_mul_64
0x000000ad T __asm___11_low_power_c_50b0e29e__low_power_save_cpu
0x0000012d T low_power_restore_cpu
0x00000139 T SVC_Handler
0x00000171 T vPortSVCHandler
0x00000195 T __asm___6_port_c_39a90d8d__prvStartFirstTask
0x000001a9 T prv_call_svc_pc
0x000001ad T PendSV_Handler
0x000001f9 T vPortGetIPSR
0x00000201 T __asm___10_app_boot_c_fcf06f29____REV16
0x00000205 T __asm___14_ble_util_buf_c_b8bf6b0e____REV16
0x00000209 T __asm___7_rwble_c_5e66ef03____REV16
0x0000020d T __asm___5_llc_c_llc_init____REV16
0x00000211 T __asm___15_llc_chmap_upd_c_ddac89ec____REV16
0x00000215 T __asm___13_llc_con_upd_c_de71abdd____REV16
0x00000219 T __asm___9_llc_dbg_c____REV16
0x0000021d T __asm___16_llc_disconnect_c_67e9d540____REV16
0x00000221 T __asm___12_llc_dl_upd_c_553d3f21____REV16
0x00000225 T __asm___13_llc_encrypt_c_8159a054____REV16
0x00000229 T __asm___15_llc_feat_exch_c_6fa097ef____REV16
0x0000022d T __asm___9_llc_hci_c_996dc4b6____REV16
0x00000231 T __asm___13_llc_le_ping_c_434cc73c____REV16
0x00000235 T __asm___10_llc_llcp_c_1a9b13a9____REV16
0x00000239 T __asm___13_llc_phy_upd_c_0294cefd____REV16
0x0000023d T __asm___10_llc_task_c_30369c55____REV16
0x00000241 T __asm___14_llc_ver_exch_c_2771d0a7____REV16
0x00000245 T __asm___5_lld_c_f7832d1e____REV16
0x00000249 T __asm___9_lld_adv_c_9a148cc9____REV16
0x0000024d T __asm___9_lld_con_c_df553fb8____REV16
0x00000251 T __asm___10_lld_init_c_b391185e____REV16
0x00000255 T __asm___13_lld_per_adv_c_77b63e9a____REV16
0x00000259 T __asm___10_lld_scan_c_01456b52____REV16
0x0000025d T __asm___10_lld_sync_c_8bd34c56____REV16
0x00000261 T __asm___10_lld_test_c_54e5e101____REV16
0x00000265 T __asm___5_llm_c_52f69136____REV16
0x00000269 T __asm___9_llm_adv_c_2ca465c6____REV16
0x0000026d T __asm___9_llm_hci_c_20aee1fa____REV16
0x00000271 T __asm___10_llm_init_c_c210a516____REV16
0x00000275 T __asm___9_llm_iso_c____REV16
0x00000279 T __asm___10_llm_scan_c_963e4937____REV16
0x0000027d T __asm___10_llm_task_c_5d3df270____REV16
0x00000281 T __asm___5_hci_c_hci_init____REV16
0x00000285 T __asm___8_hci_fc_c_fcf3c82e____REV16
0x00000289 T __asm___9_hci_msg_c_b5066cf5____REV16
0x0000028d T __asm___8_hci_tl_c_9ce329a5____REV16
0x00000291 T __asm___11_sch_alarm_c_3b4969f2____REV16
0x00000295 T __asm___9_sch_arb_c_87561c0e____REV16
0x00000299 T __asm___10_sch_plan_c_c0257ea0____REV16
0x0000029d T __asm___10_sch_prog_c_de24772c____REV16
0x000002a1 T __asm___11_sch_slice_c_de6dd3b5____REV16
0x000002a5 T __asm___5_aes_c_aes_init____REV16
0x000002a9 T __asm___9_aes_ccm_c_aes_ccm____REV16
0x000002ad T __asm___10_aes_cmac_c_fe47f34c____REV16
0x000002b1 T __asm___8_aes_k1_c_aes_k1____REV16
0x000002b5 T __asm___8_aes_k2_c_5cc4f012____REV16
0x000002b9 T __asm___8_aes_k3_c_979823b7____REV16
0x000002bd T __asm___8_aes_k4_c_8a9d130f____REV16
0x000002c1 T __asm___8_aes_s1_c_aes_s1____REV16
0x000002c5 T __asm___9_co_list_c_ca99e89f____REV16
0x000002c9 T __asm___10_co_utils_c_one_bits____REV16
0x000002cd T __asm___5_dbg_c_3a87ab48____REV16
0x000002d1 T __asm___12_dbg_mwsgen_c____REV16
0x000002d5 T __asm___12_dbg_swdiag_c____REV16
0x000002d9 T __asm___10_dbg_task_c____REV16
0x000002dd T __asm___9_dbg_trc_c____REV16
0x000002e1 T __asm___13_dbg_trc_mem_c____REV16
0x000002e5 T __asm___12_dbg_trc_tl_c____REV16
0x000002e9 T __asm___9_display_c____REV16
0x000002ed T __asm___14_display_task_c____REV16
0x000002f1 T __asm___10_ecc_p256_c_8b173798____REV16
0x000002f5 T __asm___6_h4tl_c_3a5732fe____REV16
0x000002f9 T __asm___4_ke_c_ke_init____REV16
0x000002fd T __asm___10_ke_event_c_87ccd12c____REV16
0x00000301 T __asm___8_ke_mem_c_f92e48dd____REV16
0x00000305 T __asm___8_ke_msg_c_d502b297____REV16
0x00000309 T __asm___10_ke_queue_c_5cab850e____REV16
0x0000030d T __asm___9_ke_task_c_18478d77____REV16
0x00000311 T __asm___10_ke_timer_c_3b621a08____REV16
0x00000315 T __asm___6_nvds_c____REV16
0x00000319 T __asm___9_rf_simu_c_82de3823____REV16
0x0000031d T __asm___6_rwip_c_f2b147fa____REV16
0x00000321 T __asm___13_rwip_driver_c_c12c84cf____REV16
0x00000325 T __asm___11_co_printf_c_fputc____REV16
0x00000329 T __asm___11_low_power_c_50b0e29e____REV16
0x0000032d T __asm___7_crc32_c_36f7c2a5____REV16
0x00000331 T __asm___5_md5_c_MD5_Init____REV16
0x00000335 T __asm___11_arch_main_c_uart_api____REV16
0x00000339 T __asm___12_jump_table_c_6a9c728e____REV16
0x0000033d T __asm___6_intc_c_189c7b66____REV16
0x00000341 T __asm___6_uart_c_988f2830____REV16
0x00000345 T __asm___7_flash_c_1f9d869b____REV16
0x00000349 T __asm___6_qspi_c_79b82d8e____REV16
0x0000034d T __asm___8_system_c_a51710b5____REV16
0x00000351 T __asm___6_gpio_c_a47c3f18____REV16
0x00000355 T __asm___5_ssp_c_3d1cf05e____REV16
0x00000359 T __asm___8_frspim_c_6e8f94f7____REV16
0x0000035d T __asm___5_pmu_c_7ed478f1____REV16
0x00000361 T __asm___9_apb2spi_c_a124e987____REV16
0x00000365 T __asm___6_trng_c_36387bb2____REV16
0x00000369 T __asm___6_list_c_a968f7cb____REV16
0x0000036d T __asm___7_queue_c_48e2f297____REV16
0x00000371 T __asm___7_tasks_c_f31043e3____REV16
0x00000375 T __asm___8_timers_c_a20bbafd____REV16
0x00000379 T __asm___14_event_groups_c_5f98c4ce____REV16
0x0000037d T __asm___8_heap_6_c_94e30ff9____REV16
0x00000381 T __asm___6_port_c_39a90d8d____REV16
0x00000385 T __asm___20_freertos_interface_c_bc2b671d____REV16
0x00000389 T __asm___10_app_boot_c_fcf06f29____REVSH
0x0000038d T __asm___14_ble_util_buf_c_b8bf6b0e____REVSH
0x00000391 T __asm___7_rwble_c_5e66ef03____REVSH
0x00000395 T __asm___5_llc_c_llc_init____REVSH
0x00000399 T __asm___15_llc_chmap_upd_c_ddac89ec____REVSH
0x0000039d T __asm___13_llc_con_upd_c_de71abdd____REVSH
0x000003a1 T __asm___9_llc_dbg_c____REVSH
0x000003a5 T __asm___16_llc_disconnect_c_67e9d540____REVSH
0x000003a9 T __asm___12_llc_dl_upd_c_553d3f21____REVSH
0x000003ad T __asm___13_llc_encrypt_c_8159a054____REVSH
0x000003b1 T __asm___15_llc_feat_exch_c_6fa097ef____REVSH
0x000003b5 T __asm___9_llc_hci_c_996dc4b6____REVSH
0x000003b9 T __asm___13_llc_le_ping_c_434cc73c____REVSH
0x000003bd T __asm___10_llc_llcp_c_1a9b13a9____REVSH
0x000003c1 T __asm___13_llc_phy_upd_c_0294cefd____REVSH
0x000003c5 T __asm___10_llc_task_c_30369c55____REVSH
0x000003c9 T __asm___14_llc_ver_exch_c_2771d0a7____REVSH
0x000003cd T __asm___5_lld_c_f7832d1e____REVSH
0x000003d1 T __asm___9_lld_adv_c_9a148cc9____REVSH
0x000003d5 T __asm___9_lld_con_c_df553fb8____REVSH
0x000003d9 T __asm___10_lld_init_c_b391185e____REVSH
0x000003dd T __asm___13_lld_per_adv_c_77b63e9a____REVSH
0x000003e1 T __asm___10_lld_scan_c_01456b52____REVSH
0x000003e5 T __asm___10_lld_sync_c_8bd34c56____REVSH
0x000003e9 T __asm___10_lld_test_c_54e5e101____REVSH
0x000003ed T __asm___5_llm_c_52f69136____REVSH
0x000003f1 T __asm___9_llm_adv_c_2ca465c6____REVSH
0x000003f5 T __asm___9_llm_hci_c_20aee1fa____REVSH
0x000003f9 T __asm___10_llm_init_c_c210a516____REVSH
0x000003fd T __asm___9_llm_iso_c____REVSH
0x00000401 T __asm___10_llm_scan_c_963e4937____REVSH
0x00000405 T __asm___10_llm_task_c_5d3df270____REVSH
0x00000409 T __asm___5_hci_c_hci_init____REVSH
0x0000040d T __asm___8_hci_fc_c_fcf3c82e____REVSH
0x00000411 T __asm___9_hci_msg_c_b5066cf5____REVSH
0x00000415 T __asm___8_hci_tl_c_9ce329a5____REVSH
0x00000419 T __asm___11_sch_alarm_c_3b4969f2____REVSH
0x0000041d T __asm___9_sch_arb_c_87561c0e____REVSH
0x00000421 T __asm___10_sch_plan_c_c0257ea0____REVSH
0x00000425 T __asm___10_sch_prog_c_de24772c____REVSH
0x00000429 T __asm___11_sch_slice_c_de6dd3b5____REVSH
0x0000042d T __asm___5_aes_c_aes_init____REVSH
0x00000431 T __asm___9_aes_ccm_c_aes_ccm____REVSH
0x00000435 T __asm___10_aes_cmac_c_fe47f34c____REVSH
0x00000439 T __asm___8_aes_k1_c_aes_k1____REVSH
0x0000043d T __asm___8_aes_k2_c_5cc4f012____REVSH
0x00000441 T __asm___8_aes_k3_c_979823b7____REVSH
0x00000445 T __asm___8_aes_k4_c_8a9d130f____REVSH
0x00000449 T __asm___8_aes_s1_c_aes_s1____REVSH
0x0000044d T __asm___9_co_list_c_ca99e89f____REVSH
0x00000451 T __asm___10_co_utils_c_one_bits____REVSH
0x00000455 T __asm___5_dbg_c_3a87ab48____REVSH
0x00000459 T __asm___12_dbg_mwsgen_c____REVSH
0x0000045d T __asm___12_dbg_swdiag_c____REVSH
0x00000461 T __asm___10_dbg_task_c____REVSH
0x00000465 T __asm___9_dbg_trc_c____REVSH
0x00000469 T __asm___13_dbg_trc_mem_c____REVSH
0x0000046d T __asm___12_dbg_trc_tl_c____REVSH
0x00000471 T __asm___9_display_c____REVSH
0x00000475 T __asm___14_display_task_c____REVSH
0x00000479 T __asm___10_ecc_p256_c_8b173798____REVSH
0x0000047d T __asm___6_h4tl_c_3a5732fe____REVSH
0x00000481 T __asm___4_ke_c_ke_init____REVSH
0x00000485 T __asm___10_ke_event_c_87ccd12c____REVSH
0x00000489 T __asm___8_ke_mem_c_f92e48dd____REVSH
0x0000048d T __asm___8_ke_msg_c_d502b297____REVSH
0x00000491 T __asm___10_ke_queue_c_5cab850e____REVSH
0x00000495 T __asm___9_ke_task_c_18478d77____REVSH
0x00000499 T __asm___10_ke_timer_c_3b621a08____REVSH
0x0000049d T __asm___6_nvds_c____REVSH
0x000004a1 T __asm___9_rf_simu_c_82de3823____REVSH
0x000004a5 T __asm___6_rwip_c_f2b147fa____REVSH
0x000004a9 T __asm___13_rwip_driver_c_c12c84cf____REVSH
0x000004ad T __asm___11_co_printf_c_fputc____REVSH
0x000004b1 T __asm___11_low_power_c_50b0e29e____REVSH
0x000004b5 T __asm___7_crc32_c_36f7c2a5____REVSH
0x000004b9 T __asm___5_md5_c_MD5_Init____REVSH
0x000004bd T __asm___11_arch_main_c_uart_api____REVSH
0x000004c1 T __asm___12_jump_table_c_6a9c728e____REVSH
0x000004c5 T __asm___6_intc_c_189c7b66____REVSH
0x000004c9 T __asm___6_uart_c_988f2830____REVSH
0x000004cd T __asm___7_flash_c_1f9d869b____REVSH
0x000004d1 T __asm___6_qspi_c_79b82d8e____REVSH
0x000004d5 T __asm___8_system_c_a51710b5____REVSH
0x000004d9 T __asm___6_gpio_c_a47c3f18____REVSH
0x000004dd T __asm___5_ssp_c_3d1cf05e____REVSH
0x000004e1 T __asm___8_frspim_c_6e8f94f7____REVSH
0x000004e5 T __asm___5_pmu_c_7ed478f1____REVSH
0x000004e9 T __asm___9_apb2spi_c_a124e987____REVSH
0x000004ed T __asm___6_trng_c_36387bb2____REVSH
0x000004f1 T __asm___6_list_c_a968f7cb____REVSH
0x000004f5 T __asm___7_queue_c_48e2f297____REVSH
0x000004f9 T __asm___7_tasks_c_f31043e3____REVSH
0x000004fd T __asm___8_timers_c_a20bbafd____REVSH
0x00000501 T __asm___14_event_groups_c_5f98c4ce____REVSH
0x00000505 T __asm___8_heap_6_c_94e30ff9____REVSH
0x00000509 T __asm___6_port_c_39a90d8d____REVSH
0x0000050d T __asm___20_freertos_interface_c_bc2b671d____REVSH
0x00000511 T __asm___10_app_boot_c_fcf06f29____RRX
0x00000519 T __asm___14_ble_util_buf_c_b8bf6b0e____RRX
0x00000521 T __asm___7_rwble_c_5e66ef03____RRX
0x00000529 T __asm___5_llc_c_llc_init____RRX
0x00000531 T __asm___15_llc_chmap_upd_c_ddac89ec____RRX
0x00000539 T __asm___13_llc_con_upd_c_de71abdd____RRX
0x00000541 T __asm___9_llc_dbg_c____RRX
0x00000549 T __asm___16_llc_disconnect_c_67e9d540____RRX
0x00000551 T __asm___12_llc_dl_upd_c_553d3f21____RRX
0x00000559 T __asm___13_llc_encrypt_c_8159a054____RRX
0x00000561 T __asm___15_llc_feat_exch_c_6fa097ef____RRX
0x00000569 T __asm___9_llc_hci_c_996dc4b6____RRX
0x00000571 T __asm___13_llc_le_ping_c_434cc73c____RRX
0x00000579 T __asm___10_llc_llcp_c_1a9b13a9____RRX
0x00000581 T __asm___13_llc_phy_upd_c_0294cefd____RRX
0x00000589 T __asm___10_llc_task_c_30369c55____RRX
0x00000591 T __asm___14_llc_ver_exch_c_2771d0a7____RRX
0x00000599 T __asm___5_lld_c_f7832d1e____RRX
0x000005a1 T __asm___9_lld_adv_c_9a148cc9____RRX
0x000005a9 T __asm___9_lld_con_c_df553fb8____RRX
0x000005b1 T __asm___10_lld_init_c_b391185e____RRX
0x000005b9 T __asm___13_lld_per_adv_c_77b63e9a____RRX
0x000005c1 T __asm___10_lld_scan_c_01456b52____RRX
0x000005c9 T __asm___10_lld_sync_c_8bd34c56____RRX
0x000005d1 T __asm___10_lld_test_c_54e5e101____RRX
0x000005d9 T __asm___5_llm_c_52f69136____RRX
0x000005e1 T __asm___9_llm_adv_c_2ca465c6____RRX
0x000005e9 T __asm___9_llm_hci_c_20aee1fa____RRX
0x000005f1 T __asm___10_llm_init_c_c210a516____RRX
0x000005f9 T __asm___9_llm_iso_c____RRX
0x00000601 T __asm___10_llm_scan_c_963e4937____RRX
0x00000609 T __asm___10_llm_task_c_5d3df270____RRX
0x00000611 T __asm___5_hci_c_hci_init____RRX
0x00000619 T __asm___8_hci_fc_c_fcf3c82e____RRX
0x00000621 T __asm___9_hci_msg_c_b5066cf5____RRX
0x00000629 T __asm___8_hci_tl_c_9ce329a5____RRX
0x00000631 T __asm___11_sch_alarm_c_3b4969f2____RRX
0x00000639 T __asm___9_sch_arb_c_87561c0e____RRX
0x00000641 T __asm___10_sch_plan_c_c0257ea0____RRX
0x00000649 T __asm___10_sch_prog_c_de24772c____RRX
0x00000651 T __asm___11_sch_slice_c_de6dd3b5____RRX
0x00000659 T __asm___5_aes_c_aes_init____RRX
0x00000661 T __asm___9_aes_ccm_c_aes_ccm____RRX
0x00000669 T __asm___10_aes_cmac_c_fe47f34c____RRX
0x00000671 T __asm___8_aes_k1_c_aes_k1____RRX
0x00000679 T __asm___8_aes_k2_c_5cc4f012____RRX
0x00000681 T __asm___8_aes_k3_c_979823b7____RRX
0x00000689 T __asm___8_aes_k4_c_8a9d130f____RRX
0x00000691 T __asm___8_aes_s1_c_aes_s1____RRX
0x00000699 T __asm___9_co_list_c_ca99e89f____RRX
0x000006a1 T __asm___10_co_utils_c_one_bits____RRX
0x000006a9 T __asm___5_dbg_c_3a87ab48____RRX
0x000006b1 T __asm___12_dbg_mwsgen_c____RRX
0x000006b9 T __asm___12_dbg_swdiag_c____RRX
0x000006c1 T __asm___10_dbg_task_c____RRX
0x000006c9 T __asm___9_dbg_trc_c____RRX
0x000006d1 T __asm___13_dbg_trc_mem_c____RRX
0x000006d9 T __asm___12_dbg_trc_tl_c____RRX
0x000006e1 T __asm___9_display_c____RRX
0x000006e9 T __asm___14_display_task_c____RRX
0x000006f1 T __asm___10_ecc_p256_c_8b173798____RRX
0x000006f9 T __asm___6_h4tl_c_3a5732fe____RRX
0x00000701 T __asm___4_ke_c_ke_init____RRX
0x00000709 T __asm___10_ke_event_c_87ccd12c____RRX
0x00000711 T __asm___8_ke_mem_c_f92e48dd____RRX
0x00000719 T __asm___8_ke_msg_c_d502b297____RRX
0x00000721 T __asm___10_ke_queue_c_5cab850e____RRX
0x00000729 T __asm___9_ke_task_c_18478d77____RRX
0x00000731 T __asm___10_ke_timer_c_3b621a08____RRX
0x00000739 T __asm___6_nvds_c____RRX
0x00000741 T __asm___9_rf_simu_c_82de3823____RRX
0x00000749 T __asm___6_rwip_c_f2b147fa____RRX
0x00000751 T __asm___13_rwip_driver_c_c12c84cf____RRX
0x00000759 T __asm___11_co_printf_c_fputc____RRX
0x00000761 T __asm___11_low_power_c_50b0e29e____RRX
0x00000769 T __asm___7_crc32_c_36f7c2a5____RRX
0x00000771 T __asm___5_md5_c_MD5_Init____RRX
0x00000779 T __asm___11_arch_main_c_uart_api____RRX
0x00000781 T __asm___12_jump_table_c_6a9c728e____RRX
0x00000789 T __asm___6_intc_c_189c7b66____RRX
0x00000791 T __asm___6_uart_c_988f2830____RRX
0x00000799 T __asm___7_flash_c_1f9d869b____RRX
0x000007a1 T __asm___6_qspi_c_79b82d8e____RRX
0x000007a9 T __asm___8_system_c_a51710b5____RRX
0x000007b1 T __asm___6_gpio_c_a47c3f18____RRX
0x000007b9 T __asm___5_ssp_c_3d1cf05e____RRX
0x000007c1 T __asm___8_frspim_c_6e8f94f7____RRX
0x000007c9 T __asm___5_pmu_c_7ed478f1____RRX
0x000007d1 T __asm___9_apb2spi_c_a124e987____RRX
0x000007d9 T __asm___6_trng_c_36387bb2____RRX
0x000007e1 T __asm___6_list_c_a968f7cb____RRX
0x000007e9 T __asm___7_queue_c_48e2f297____RRX
0x000007f1 T __asm___7_tasks_c_f31043e3____RRX
0x000007f9 T __asm___8_timers_c_a20bbafd____RRX
0x00000801 T __asm___14_event_groups_c_5f98c4ce____RRX
0x00000809 T __asm___8_heap_6_c_94e30ff9____RRX
0x00000811 T __asm___6_port_c_39a90d8d____RRX
0x00000819 T __asm___20_freertos_interface_c_bc2b671d____RRX
0x00000821 T Reset_Handler
0x00000853 T set_handlemode_sp
0x00000859 T NMI_Handler
0x0000085b T HardFault_Handler
0x0000085d T MemManage_Handler
0x0000085f T BusFault_Handler
0x00000861 T UsageFault_Handler
0x00000865 T DebugMon_Handler
0x0000086b T EXTI0_Handler
0x0000086d T EXTI1_Handler
0x0000086f T EXTI2_Handler
0x00000871 T EXTI3_Handler
0x00000873 T EXTI4_Handler
0x00000875 T ext_int_isr
0x00000879 T i2s_isr
0x0000087b T pdm_isr
0x00000881 T CPU_SR_Save
0x00000891 T CPU_SR_Restore
0x00000897 T GLOBAL_INT_STOP
0x0000089b T GLOBAL_INT_START
0x000008a1 T rand
0x000008b5 T srand
0x000008c5 T __aeabi_memcpy
0x000008c5 T __aeabi_memcpy4
0x000008c5 T __aeabi_memcpy8
0x000008e9 T __aeabi_memset
0x000008e9 T __aeabi_memset4
0x000008e9 T __aeabi_memset8
0x000008f7 T __aeabi_memclr
0x000008f7 T __aeabi_memclr4
0x000008f7 T __aeabi_memclr8
0x000008fb T _memset$wrapper
0x0000090d T memcmp
0x00000929 T __scatterload
0x00000929 T __scatterload_rt2
0x0000094d T __decompress
0x0000094d T __decompress0
0x00000989 T Add2SelfBigHex256
0x000009d1 T AddBigHex256
0x00000a1d T AddBigHexModP256
0x00000a6d T AddP256
0x00000a99 T AddPdiv2_256
0x00000bad T FPB_CompSet
0x00000bd5 T GF_Jacobian_Point_Addition256
0x00000f91 T GF_Jacobian_Point_Double256
0x00001109 T GF_Point_Jacobian_To_Affine256
0x00001135 T MD5_Final
0x0000120d T MD5_Init
0x00001231 T MD5_Update
0x000012ad T MultiplyBigHexByUint32_256
0x000012f1 T MultiplyBigHexModP256
0x00001431 T MultiplyByU32ModP256
0x00001461 T NVIC_DisableIRQ
0x0000147b T NVIC_EnableIRQ
0x00001495 T NVIC_SetPriority
0x000014d5 T SubtractBigHex256
0x00001529 T SubtractBigHexMod256
0x00001611 T SubtractBigHexUint32_256
0x00001667 T SubtractFromSelfBigHex256
0x000016b9 T SubtractFromSelfBigHexSign256
0x00001775 T SysTick_Handler
0x000017a1 T __scatterload_copy
0x000017af T __scatterload_null
0x000017b1 T __scatterload_zeroinit
0x000017bf T aes_alloc
0x000017d5 T aes_ccm
0x00001b05 T aes_cmac
0x00001b31 T aes_cmac_continue
0x00001c75 T aes_cmac_start
0x00001cad T aes_encrypt
0x00001d01 T aes_init
0x00001d29 T aes_k1
0x00001da1 T aes_k2
0x00001ec5 T aes_k3
0x00001f31 T aes_k4
0x00001fa5 T aes_rand
0x00001fd5 T aes_result_handler
0x0000202d T aes_s1
0x00002051 T aes_shift_left_128
0x00002075 T aes_start
0x000020b1 T aes_xor_128
0x000020c9 T apb2spi_init
0x000020dd T apb2spi_read
0x00002145 T apb2spi_write
0x000021a5 T app_boot
0x00002241 T app_boot_detect_storage_type
0x00002261 T app_boot_get_buffer
0x00002269 T app_boot_get_storage_type
0x00002279 T app_boot_host_comm
0x000022ed T app_boot_host_comm_loop
0x00002351 T app_boot_init
0x00002399 T app_boot_load
0x0000240d T app_boot_load_data
0x00002439 T app_boot_process_cmd
0x000026c1 T app_boot_save_data
0x000026e1 T app_boot_send_rsp
0x000026f1 T app_boot_serial_gets
0x00002735 T app_boot_set_storage_type
0x00002749 T bigHexInversion256
0x00002965 T ble_util_buf_acl_tx_alloc
0x0000297d T ble_util_buf_acl_tx_elt_get
0x000029a5 T ble_util_buf_acl_tx_free
0x000029d1 T ble_util_buf_adv_tx_alloc
0x000029e5 T ble_util_buf_adv_tx_free
0x00002a11 T ble_util_buf_advexthdr_alloc
0x00002a75 T ble_util_buf_advexthdr_free
0x00002ab9 T ble_util_buf_init
0x00002c25 T ble_util_buf_init_env
0x00002c35 T ble_util_buf_llcp_tx_alloc
0x00002c41 T ble_util_buf_llcp_tx_free
0x00002c65 T ble_util_buf_rx_alloc
0x00002c8d T ble_util_buf_rx_free
0x000034a7 T co_bdaddr_compare
0x000034c3 T co_ble_pkt_dur_in_us
;0x000034f5 T co_delay_100us
;0x00003541 T co_delay_10us
0x0000358d T co_list_extract
0x000035bd T co_list_extract_after
0x000035d5 T co_list_extract_sublist
0x000035e9 T co_list_find
0x000035ff T co_list_init
0x00003607 T co_list_insert_after
0x00003631 T co_list_insert_before
0x00003659 T co_list_merge
0x00003671 T co_list_pool_init
0x000036d5 T co_list_pop_front
0x000036e9 T co_list_push_back
0x000036fd T co_list_push_back_sublist
0x00003711 T co_list_push_front
0x0000371d T co_list_size
0x00003731 T co_nb_good_le_channels
0x00003759 T co_printf
0x0000376d T co_sprintf
0x00003781 T co_util_pack
0x00003963 T co_util_unpack
0x00003b21 T crc32
0x00003bd5 T dbg_platform_reset_complete
0x00003c09 T divideByTwo256
0x00003c49 T dl_upd_proc_start
0x00003cbd T eTaskConfirmSleepModeStatus
0x00003ced T ecc_abort_key256_generation
0x00003d39 T ecc_gen_new_public_key
0x00003d55 T ecc_gen_new_secret_key
0x00003e45 T ecc_generate_key256
0x000040f1 T ecc_get_debug_Keys
0x00004119 T ecc_init
0x000046a5 T em_ble_generate_base_address_table
0x00004755 T flash_enter_deep_sleep
;0x00004775 T flash_erase
0x000047e9 T flash_exit_deep_sleep
;0x00004809 T flash_read
0x00004871 T flash_read_status
;0x00004899 T flash_write
0x00004951 T flash_write_status
0x0000499d T fputc
0x000049ed T freertos_baseband_restore_done
0x00004a8d T frspim_init
;0x00004aad T frspim_rd
;0x00004b01 T frspim_wr
0x00004b51 T get_crc_table
0x00004b59 T get_stack_limit
0x00004b65 T get_stack_usage
0x00004b79 T gpio_get_pin_value
0x00004bb5 T gpio_set_dir
0x00004c09 T gpio_set_pin_value
0x00004d2d T h4tl_init
0x00004f05 T h4tl_start
0x00004f25 T h4tl_stop
0x0000501d T h4tl_write
0x00005071 T hci_acl_data_handler
0x00005111 T hci_acl_tx_data_alloc
0x0000517d T hci_acl_tx_data_received
0x000051d1 T hci_basic_cmd_send_2_controller
0x000052a9 T hci_ble_conhdl_register
0x000052b9 T hci_ble_conhdl_unregister
0x0000539b T hci_cmd_get_max_param_size
0x000053ad T hci_cmd_received
0x000055bd T hci_command_handler
0x000055f1 T hci_disconnect_cmd_handler
0x00005705 T hci_evt_mask_set
0x00005755 T hci_fc_acl_buf_size_set
0x00005769 T hci_fc_acl_en
0x000057a1 T hci_fc_acl_packet_sent
0x000057b5 T hci_fc_check_host_available_nb_acl_packets
0x000057d9 T hci_fc_host_nb_acl_pkts_complete
0x000057f1 T hci_fc_init
0x000058f1 T hci_init
0x00005939 T hci_le_add_dev_to_per_adv_list_cmd_handler
0x00005c79 T hci_le_clear_adv_sets_cmd_handler
0x00005d05 T hci_le_clear_per_adv_list_cmd_handler
0x00005e09 T hci_le_con_upd_cmd_handler
0x00005f29 T hci_le_create_con_cancel_cmd_handler
0x00005fa1 T hci_le_create_con_cmd_handler
0x0000660d T hci_le_ext_create_con_cmd_handler
0x00006b3d T hci_le_ltk_req_neg_reply_cmd_handler
0x00006bcd T hci_le_ltk_req_reply_cmd_handler
0x00006c45 T hci_le_per_adv_create_sync_cancel_cmd_handler
0x00006cc9 T hci_le_per_adv_create_sync_cmd_handler
0x00006d95 T hci_le_per_adv_term_sync_cmd_handler
0x00006e39 T hci_le_rd_adv_ch_tx_pw_cmd_handler
0x00006ebd T hci_le_rd_chnl_map_cmd_handler
0x00006fe1 T hci_le_rd_max_adv_data_len_cmd_handler
0x00007061 T hci_le_rd_nb_supp_adv_sets_cmd_handler
0x00007105 T hci_le_rd_per_adv_list_size_cmd_handler
0x0000713d T hci_le_rd_phy_cmd_handler
0x0000718d T hci_le_rd_rem_feats_cmd_handler
0x00007349 T hci_le_rem_con_param_req_neg_reply_cmd_handler
0x000073a9 T hci_le_rem_con_param_req_reply_cmd_handler
0x00007435 T hci_le_rmv_adv_set_cmd_handler
0x000074a9 T hci_le_rmv_dev_from_per_adv_list_cmd_handler
0x00007691 T hci_le_set_adv_data_cmd_handler
0x00007769 T hci_le_set_adv_en_cmd_handler
0x00007979 T hci_le_set_adv_param_cmd_handler
0x00007ad9 T hci_le_set_adv_set_rand_addr_cmd_handler
0x00007b59 T hci_le_set_data_len_cmd_handler
0x00007cd9 T hci_le_set_ext_adv_data_cmd_handler
0x00007edd T hci_le_set_ext_adv_en_cmd_handler
0x000083cd T hci_le_set_ext_adv_param_cmd_handler
0x000085c5 T hci_le_set_ext_scan_en_cmd_handler
0x00008865 T hci_le_set_ext_scan_param_cmd_handler
0x00008a91 T hci_le_set_ext_scan_rsp_data_cmd_handler
0x00008c8d T hci_le_set_per_adv_data_cmd_handler
0x00008e19 T hci_le_set_per_adv_en_cmd_handler
0x00008fc5 T hci_le_set_per_adv_param_cmd_handler
0x000090c5 T hci_le_set_phy_cmd_handler
0x000092a1 T hci_le_set_scan_en_cmd_handler
0x000094b9 T hci_le_set_scan_param_cmd_handler
0x000095f9 T hci_le_set_scan_rsp_data_cmd_handler
0x000096d1 T hci_le_start_enc_cmd_handler
0x000098a9 T hci_look_for_cmd_desc
0x000098f9 T hci_look_for_evt_desc
0x00009921 T hci_look_for_le_evt_desc
0x00009985 T hci_rd_auth_payl_to_cmd_handler
0x00009a99 T hci_rd_rem_ver_info_cmd_handler
0x00009b29 T hci_rd_rssi_cmd_handler
0x00009b95 T hci_rd_tx_pwr_lvl_cmd_handler
0x00009c25 T hci_send_2_controller
0x00009c9d T hci_send_2_host
0x00009dc9 T hci_tl_init
0x00009df5 T hci_tl_send
0x0000a009 T hci_vs_set_pref_slave_latency_cmd_handler
0x0000a04d T hci_wr_auth_payl_to_cmd_handler
0x0000a121 T intc_init
0x0000a1d5 T ke_check_malloc
0x0000a259 T ke_event_callback_set
0x0000a271 T ke_event_clear
0x0000a299 T ke_event_flush
0x0000a2a5 T ke_event_get
0x0000a2cd T ke_event_get_all
0x0000a2d9 T ke_event_init
0x0000a2e5 T ble_stack_schedule
0x0000a329 T ke_event_set
0x0000a369 T ke_flush
0x0000a3a5 T ke_free
0x0000a475 T ke_get_mem_usage
0x0000a485 T ke_init
0x0000a4b9 T ke_is_free
0x0000a4cd T ke_malloc
0x0000a5a1 T ke_mem_init
0x0000a5f1 T ke_mem_is_empty
0x0000a625 T ke_msg_alloc
0x0000a65b T ke_msg_dest_id_get
0x0000a661 T ke_msg_discard
0x0000a665 T ke_msg_forward
0x0000a671 T ke_msg_forward_new_id
0x0000a683 T ke_msg_free
0x0000a687 T ke_msg_in_queue
0x0000a693 T ke_msg_save
0x0000a699 T ke_msg_send
0x0000a6c5 T ke_msg_send_basic
0x0000a6d5 T ke_msg_src_id_get
0x0000a6db T ke_queue_extract
0x0000a719 T ke_queue_insert
0x0000a749 T ke_sleep_check
0x0000a759 T ke_state_get
0x0000a789 T ke_state_set
0x0000a7c9 T ke_task_check
0x0000a7f5 T ke_task_create
;0x0000a839 T ke_task_delete
0x0000a8d1 T ke_task_init
0x0000a8e9 T ke_task_msg_flush
0x0000aa09 T ke_time
0x0000aa45 T ke_timer_active
0x0000aa6d T ke_timer_adjust_all
0x0000aa85 T ke_timer_clear
0x0000aaed T ke_timer_init
0x0000ab4d T ke_timer_set
0x0000abf9 T ll_channel_map_ind_handler
0x0000ac89 T ll_connection_param_req_handler
0x0000ad61 T ll_connection_param_rsp_handler
0x0000adf1 T ll_connection_update_ind_handler
0x0000af09 T ll_enc_req_handler
0x0000afc5 T ll_enc_rsp_handler
0x0000b031 T ll_feature_req_handler
0x0000b0a1 T ll_feature_rsp_handler
0x0000b0f1 T ll_length_req_handler
0x0000b145 T ll_length_rsp_handler
0x0000b1c1 T ll_min_used_channels_ind_handler
0x0000b245 T ll_pause_enc_req_handler
0x0000b2b1 T ll_pause_enc_rsp_handler
0x0000b309 T ll_phy_req_handler
0x0000b3b9 T ll_phy_rsp_handler
0x0000b431 T ll_phy_update_ind_handler
0x0000b4bf T ll_ping_req_handler
0x0000b4d3 T ll_ping_rsp_handler
0x0000b52f T ll_slave_feature_req_handler
0x0000b535 T ll_start_enc_req_handler
0x0000b585 T ll_start_enc_rsp_handler
0x0000b5d5 T ll_terminate_ind_handler
0x0000b621 T ll_version_ind_handler
0x0000b6b1 T llc_auth_payl_nearly_to_handler
0x0000b715 T llc_auth_payl_real_to_handler
0x0000b7a1 T llc_cleanup
0x0000b809 T llc_cmd_cmp_send
0x0000b829 T llc_cmd_stat_send
0x0000b845 T llc_con_move_cbk
0x0000b97d T llc_disconnect
0x0000ba8d T llc_encrypt_ind_handler
0x0000bdf1 T llc_init
0x0000be4d T llc_init_term_proc
0x0000bf09 T llc_le_ping_restart
0x0000bf45 T llc_le_ping_set
0x0000bfe9 T llc_ll_reject_ind_pdu_send
0x0000c08d T llc_llcp_send
0x0000c10d T llc_llcp_state_set
0x0000c1c1 T llc_llcp_tx_check
0x0000cee1 T llc_op_ch_map_upd_ind_handler
0x0000cf45 T llc_op_con_upd_ind_handler
0x0000cfd9 T llc_op_disconnect_ind_handler
0x0000d049 T llc_op_dl_upd_ind_handler
0x0000d0b9 T llc_op_encrypt_ind_handler
0x0000d11d T llc_op_feats_exch_ind_handler
0x0000d185 T llc_op_le_ping_ind_handler
0x0000d1d9 T llc_op_phy_upd_ind_handler
0x0000d261 T llc_op_ver_exch_ind_handler
0x0000d3f5 T llc_proc_collision_check
0x0000d41d T llc_proc_err_ind
0x0000d44d T llc_proc_get
0x0000d465 T llc_proc_id_get
0x0000d481 T llc_proc_reg
0x0000d4b5 T llc_proc_state_get
0x0000d4b9 T llc_proc_state_set
0x0000d4bd T llc_proc_timer_pause_set
0x0000d519 T llc_proc_timer_set
0x0000d571 T llc_proc_unreg
0x0000dde9 T llc_start
0x0000df85 T llc_stop
0x0000dfb9 T llc_stopped_ind_handler
0x0000e091 T lld_aa_gen
0x0000e10d T lld_acl_rx_ind_handler
0x0000e191 T lld_acl_tx_cfm_handler
0x0000e265 T lld_adv_adv_data_update
0x0000e529 T lld_adv_duration_update
0x0000e5fd T lld_adv_end_ind_handler
0x0000f995 T lld_adv_init
0x0000fd1d T lld_adv_rand_addr_update
#0x0000fd89 T lld_adv_rep_ind_handler
0x00010235 T lld_adv_restart
0x00010391 T lld_adv_scan_rsp_data_update
0x000103d5 T lld_adv_start
0x00010ac1 T lld_adv_stop
0x00010cdd T lld_adv_sync_info_update
0x00010d0d T lld_calc_aux_rx
0x00010e29 T lld_ch_assess_data_get
0x00010e31 T lld_ch_map_upd_cfm_handler
0x00010e69 T lld_channel_assess
0x00010e99 T lld_con_activity_act_offset_compute
0x00010f4d T lld_con_activity_offset_compute
0x00010fdd T lld_con_ch_map_update
0x00011105 T lld_con_current_tx_power_get
0x0001115d T lld_con_data_flow_set
0x00011211 T lld_con_data_len_update
0x00011269 T lld_con_data_tx
0x00011319 T lld_con_enc_key_load
0x000113d5 T lld_con_estab_ind_handler
0x000113fd T lld_con_event_counter_get
0x00011421 T lld_con_evt_canceled_cbk
0x00011461 T lld_con_evt_start_cbk
0x00011651 T lld_con_evt_time_update
0x000118b9 T lld_con_init
0x000118f5 T lld_con_llcp_tx
0x00011999 T lld_con_max_lat_calc
0x000119f9 T lld_con_offset_get
0x00011a3d T lld_con_offset_upd_ind_handler
0x00011a81 T lld_con_param_upd_cfm_handler
0x00011aed T lld_con_param_update
0x00011b75 T lld_con_phys_update
0x00011bdd T lld_con_pref_slave_latency_set
0x00011c0d T lld_con_rssi_get
0x00011ebd T lld_con_rx_enc
0x00011f05 T lld_con_sched
0x00012239 T lld_con_start
0x000125f5 T lld_con_stop
0x00012761 T lld_con_tx_enc
0x000127f5 T lld_con_tx_len_update_for_intv
0x00012839 T lld_con_tx_len_update_for_rate
0x00012c15 T lld_disc_ind_handler
0x00012c51 T lld_init
0x00012d37 T lld_init_connect_req_pack
0x00012e65 T lld_init_end_ind_handler
0x0001342d T lld_init_init
0x00013c31 T lld_init_start
0x0001431d T lld_init_stop
0x000143cd T lld_llcp_rx_ind_handler
0x00014531 T lld_llcp_tx_cfm_handler
0x00014575 T lld_per_adv_ch_map_update
0x000147a5 T lld_per_adv_data_update
0x000147e9 T lld_per_adv_end_ind_handler
0x00014ed9 T lld_per_adv_init
0x00014f15 T lld_per_adv_init_info_get
0x00014f5d T lld_per_adv_list_add
0x00015005 T lld_per_adv_list_rem
0x0001507d T lld_per_adv_rep_ind_handler
0x00015365 T lld_per_adv_rx_end_ind_handler
0x00015541 T lld_per_adv_start
0x000157b1 T lld_per_adv_stop
0x00015841 T lld_per_adv_sync_info_get
0x0001589d T lld_phy_upd_cfm_handler
0x000158d5 T lld_ral_search
0x00015935 T lld_read_clock
0x00015941 T lld_res_list_add
0x00015a49 T lld_res_list_clear
0x00015a7d T lld_res_list_is_empty
0x00015ab5 T lld_res_list_local_rpa_get
0x00015af9 T lld_res_list_peer_check
0x00015b31 T lld_res_list_peer_rpa_get
0x00015b75 T lld_res_list_peer_update
0x00015bb1 T lld_res_list_priv_mode_update
0x00015bf9 T lld_res_list_rem
0x00015c31 T lld_rpa_renew
0x00015d7d T lld_rxdesc_check
0x00015db5 T lld_rxdesc_free
0x00015e15 T lld_scan_create_sync
0x00015e55 T lld_scan_create_sync_cancel
0x00015f35 T lld_scan_end_ind_handler
0x00016255 T lld_scan_init
0x00016295 T lld_scan_params_update
0x00016c05 T lld_scan_req_ind_handler
0x00016c51 T lld_scan_restart
0x00016fe9 T lld_scan_start
0x000174b1 T lld_scan_stop
0x00017669 T lld_sync_ch_map_update
0x000178b9 T lld_sync_init
0x00017ead T lld_sync_start
0x000180a5 T lld_sync_start_req_handler
0x000181a1 T lld_sync_stop
0x0001822d T lld_test_end_ind_handler
0x00018395 T lld_test_init
0x000183a9 T lld_test_start
0x00018611 T lld_test_stop
0x000186c1 T lld_white_list_add
0x00018759 T lld_white_list_rem
0x000187d1 T llm_activity_free_get
0x00018835 T llm_activity_free_set
0x000188b5 T llm_adv_hdl_to_id
0x00018ae1 T llm_ch_map_update
0x00018c31 T llm_ch_map_update_ind_handler
0x00018c9d T llm_cmd_cmp_send
0x00018cb9 T llm_cmd_stat_send
0x00018cd5 T llm_dev_list_empty_entry
0x00018cf9 T llm_dev_list_search
0x00018d99 T llm_hci_command_handler
0x00018dcd T llm_init
0x00018efd T llm_init_act_info_buf
0x00018f09 T llm_is_dev_connected
0x00018f79 T llm_is_dev_synced
0x00019099 T llm_le_evt_mask_check
0x000190b9 T llm_le_features_get
0x000190c1 T llm_link_disc
0x00019135 T llm_master_ch_map_get
0x00019189 T llm_plan_elt_get
0x00019215 T llm_rx_path_comp_get
0x00019221 T llm_scan_sync_acad_attach
0x00019265 T llm_tx_path_comp_get
0x00019271 T low_power_enter_sleep
0x0001927d T low_power_restore
0x0001937d T low_power_save
0x000193fd T main
0x00019545 T notEqual256
0x00019565 T pcTaskGetName
0x00019575 T pcTimerGetName
0x00019579 T phy_upd_proc_start
0x000195d1 T platform_reset
0x000195f9 T pmu_calibration_start
0x00019679 T pmu_calibration_stop
;0x00019685 T pmu_clear_isr_state
0x000196a9 T pmu_disable_isr
0x000196c9 T pmu_disable_isr2
0x000196e9 T pmu_enable_isr
0x00019709 T pmu_enable_isr2
0x00019729 T pmu_first_power_on
0x00019751 T pmu_get_isr_state
0x00019761 T pmu_get_rc_clk
0x000197a9 T pmu_gpio_set_dir
0x000197e1 T pmu_init
0x000197fd T pmu_isr
0x0001993d T pmu_port_set_mux
0x00019975 T pmu_port_wakeup_func_clear
;0x00019995 T pmu_port_wakeup_func_set
0x00019a4d T pmu_set_on_off_mode
0x00019a81 T print
0x0001a2d9 T pvPortMalloc
0x0001a305 T pvTaskIncrementMutexHeldCount
0x0001a31d T pvTimerGetTimerID
0x0001a331 T pxPortInitialiseStack
0x0001a355 T qspi_cfg_set_baudrate
0x0001a369 T qspi_flash_enable_quad
0x0001a3dd T qspi_flash_init
0x0001a401 T qspi_flash_init_controller
0x0001a575 T qspi_stig_cmd
0x0001a855 T rf_em_init
0x0001a879 T rf_init_api
0x0001a8b1 T rf_init_controller
0x0001a905 T rf_init_rom
0x0001a97d T rwble_init
0x0001a9bd T rwble_isr
0x0001aa1d T rwble_sleep_enter
0x0001aa35 T rwble_sleep_wakeup_end
0x0001aa45 T rwip_active_check
0x0001aa49 T rwip_aes_encrypt
0x0001aaf1 T rwip_driver_init
0x0001abb5 T rwip_eif_get
0x0001abc5 T rwip_init
0x0001ac5d T rwip_isr
0x0001ace9 T rwip_lpcycles_2_hus
0x0001ad11 T rwip_prevent_sleep_clear
0x0001ad31 T rwip_prevent_sleep_set
0x0001ad51 T rwip_reset
0x0001adb1 T rwip_schedule
0x0001adc5 T rwip_sleep
0x0001adfd T rwip_sleep_div_64
0x0001ae35 T rwip_sleep_time_calc
0x0001aef1 T rwip_slot_2_lpcycles
0x0001af1d T rwip_sw_int_req
0x0001af37 T rwip_time_get
0x0001af7d T rwip_timer_10ms_set
0x0001afdd T rwip_timer_hs_set
0x0001b025 T rwip_timer_hus_set
0x0001b059 T rwip_us_2_lpcycles
0x0001b181 T sch_alarm_clear
0x0001b1c5 T sch_alarm_init
0x0001b22d T sch_alarm_set
0x0001b2bd T sch_alarm_timer_isr
0x0001b3c5 T sch_arb_elt_cancel
0x0001b49d T sch_arb_event_start_isr
0x0001b53d T sch_arb_init
0x0001b559 T sch_arb_insert
0x0001b7b5 T sch_arb_remove
0x0001b815 T sch_arb_sw_isr
0x0001b839 T sch_plan_chk
0x0001b845 T sch_plan_init
0x0001bb19 T sch_plan_position_range_compute
0x0001bc3d T sch_plan_rem
0x0001bc49 T sch_plan_req
0x0001bc7d T sch_plan_set
0x0001bce1 T sch_prog_end_isr
0x0001bd6d T sch_prog_init
0x0001bda1 T sch_prog_push
0x0001bed1 T sch_prog_rx_isr
0x0001bef1 T sch_prog_skip_isr
0x0001bf85 T sch_prog_tx_isr
0x0001bfa5 T sch_slice_bg_add
0x0001bfb9 T sch_slice_bg_remove
0x0001bfcd T sch_slice_compute
0x0001c059 T sch_slice_fg_add
0x0001c079 T sch_slice_fg_remove
0x0001c0ad T sch_slice_init
0x0001c0d5 T sch_slice_per_add
0x0001c0fd T sch_slice_per_remove
0x0001c121 T specialModP256
0x0001c1e5 T ssp_clear_rx_fifo
0x0001c1f7 T ssp_disable
0x0001c201 T ssp_enable
0x0001c20b T ssp_get_data
0x0001c22d T ssp_init
0x0001c295 T ssp_put_byte
0x0001c29f T ssp_put_data
0x0001c2b5 T ssp_reconfigure
0x0001c301 T ssp_wait_busy_bit
0x0001c309 T ssp_write_then_read
0x0001c371 T system_get_pclk
0x0001c385 T system_get_pclk_config
0x0001c391 T system_init
0x0001c3cd T system_set_cache_config
0x0001c3dd T system_set_pclk
0x0001c3fd T system_set_port_mux
0x0001c417 T system_set_port_pull
0x0001c4d5 T trng_init
0x0001c4d9 T trng_read_rand_num
0x0001c531 T uart0_isr
0x0001c591 T uart0_read_for_hci
0x0001c5a9 T uart0_write_for_hci
0x0001c5d1 T uart1_isr
0x0001c631 T uart1_read_for_hci
0x0001c649 T uart1_write_for_hci
0x0001c671 T uart_finish_transfers
0x0001c679 T uart_flow_off
0x0001c67d T uart_flow_on
0x0001c67f T uart_flush_rxfifo_noint
0x0001c691 T uart_get_data_nodelay_noint
0x0001c6b5 T uart_get_data_noint
0x0001c6d9 T uart_init
0x0001c779 T uart_put_data_noint
0x0001c79d T uart_putc_noint
0x0001c7a7 T uart_putc_noint_no_wait
0x0001c7ab T uart_read
0x0001c7cd T uart_reset_register
0x0001c7e1 T uart_set_fifo
0x0001c7e5 T uart_write
0x0001c80d T ulTaskNotifyTake
0x0001c875 T uxListRemove
0x0001c89b T uxQueueMessagesWaiting
0x0001c8ad T uxQueueMessagesWaitingFromISR
0x0001c8b1 T uxQueueSpacesAvailable
0x0001c8c9 T uxTaskGetNumberOfTasks
0x0001c8d5 T uxTaskPriorityGet
0x0001c8f1 T uxTaskPriorityGetFromISR
0x0001c915 T uxTaskResetEventItemValue
0x0001c92d T vEventGroupClearBitsCallback
0x0001c931 T vEventGroupDelete
0x0001c95d T vEventGroupSetBitsCallback
0x0001c961 T vListInitialise
0x0001c977 T vListInitialiseItem
0x0001c97d T vListInsert
0x0001c9ad T vListInsertEnd
0x0001c9c5 T vPortEndScheduler
0x0001c9c9 T vPortEnterCritical
0x0001c9e5 T vPortExitCritical
0x0001c9f9 T vPortFree
0x0001ca15 T vPortSetupTimerInterrupt
0x0001ca69 T vPortSuppressTicksAndSleep
0x0001cb81 T vQueueDelete
0x0001cb85 T vQueueWaitForMessageRestricted
0x0001cbc9 T vTaskDelay
0x0001cbf9 T vTaskDelayUntil
0x0001cc4d T vTaskDelete
0x0001cce5 T vTaskEndScheduler
0x0001cd01 T vTaskInternalSetTimeOutState
0x0001cd11 T vTaskMissedYield
0x0001cd1d T vTaskNotifyGiveFromISR
0x0001cda9 T vTaskPlaceOnEventList
0x0001cdc9 T vTaskPlaceOnEventListRestricted
0x0001cdf1 T vTaskPlaceOnUnorderedEventList
0x0001ce1d T vTaskPriorityDisinheritAfterTimeout
0x0001ce9d T vTaskPrioritySet
0x0001cf51 T vTaskRemoveFromUnorderedEventList
0x0001cf9d T vTaskResume
0x0001d00d T vTaskSetTimeOutState
0x0001d02d T vTaskStartScheduler
0x0001d089 T vTaskStepTick
0x0001d099 T vTaskSuspend
0x0001d141 T vTaskSuspendAll
0x0001d151 T vTaskSwitchContext
0x0001d195 T vTimerSetTimerID
0x0001d1a9 T xEventGroupClearBits
0x0001d1c3 T xEventGroupCreate
0x0001d1dd T xEventGroupGetBitsFromISR
0x0001d1f7 T xEventGroupSetBits
0x0001d25d T xEventGroupSync
0x0001d2e9 T xEventGroupWaitBits
0x0001d399 T xPortStartScheduler
0x0001d3c9 T xQueueCreateCountingSemaphore
0x0001d3dd T xQueueCreateMutex
0x0001d401 T xQueueGenericCreate
0x0001d439 T xQueueGenericReset
0x0001d4ad T xQueueGenericSend
0x0001d5a5 T xQueueGenericSendFromISR
0x0001d60b T xQueueGiveFromISR
0x0001d661 T xQueueGiveMutexRecursive
0x0001d68b T xQueueIsQueueEmptyFromISR
0x0001d697 T xQueueIsQueueFullFromISR
0x0001d6a9 T xQueuePeek
0x0001d791 T xQueuePeekFromISR
0x0001d7bd T xQueueReceive
0x0001d8ad T xQueueReceiveFromISR
0x0001d911 T xQueueSemaphoreTake
0x0001da39 T xQueueTakeMutexRecursive
0x0001da69 T xTaskCheckForTimeOut
0x0001dab9 T xTaskCreate
0x0001db11 T xTaskGenericNotify
0x0001dbb9 T xTaskGenericNotifyFromISR
0x0001dc75 T xTaskGetCurrentTaskHandle
0x0001dc81 T xTaskGetSchedulerState
0x0001dc9d T xTaskGetTickCount
0x0001dca9 T xTaskGetTickCountFromISR
0x0001dcb5 T xTaskIncrementTick
0x0001dd69 T xTaskNotifyStateClear
0x0001dd9d T xTaskNotifyWait
0x0001de29 T xTaskPriorityDisinherit
0x0001de99 T xTaskPriorityInherit
0x0001df2d T xTaskRemoveFromEventList
0x0001df99 T xTaskResumeAll
0x0001e04d T xTaskResumeFromISR
0x0001e0c1 T xTimerCreate
0x0001e0f9 T xTimerCreateTimerTask
0x0001e135 T xTimerGenericCommand
0x0001e17d T xTimerGetExpiryTime
0x0001e181 T xTimerGetPeriod
0x0001e185 T xTimerGetTimerDaemonTaskHandle
0x0001e191 T xTimerIsTimerActive
0x0001e1ac D app_boot_conn_success
0x0001e1ae D app_boot_read_en_magic
0x0001e1b3 D app_boot_conn_req
0x0001e1bb D app_boot_conn_ack
0x0001e4b4 D TASK_DESC_LLC
0x0001e4c0 D LLM_AA_CT2
0x0001e4c2 D LLM_AA_CT1
0x0001e4c8 D connect_req_dur_tab
0x0001e4d0 D byte_tx_time
0x0001e4d8 D fixed_tx_time
0x0001e4e0 D lld_init_max_aux_dur_tab
0x0001e4e8 D lld_scan_map_legacy_pdu_to_evt_type
0x0001e4f0 D lld_scan_max_aux_dur_tab
0x0001e4f8 D lld_sync_max_aux_dur_tab
0x0001e844 D TASK_DESC_LLM
0x0001e860 D hci_cmd_desc_tab_lk_ctrl
0x0001e878 D hci_cmd_desc_tab_ctrl_bb
0x0001e8e4 D hci_cmd_desc_tab_info_par
0x0001e914 D hci_cmd_desc_tab_stat_par
0x0001e920 D hci_cmd_desc_tab_le
0x0001ecbc D hci_cmd_desc_tab_vs
0x0001ece0 D hci_cmd_desc_root_tab
0x0001ed10 D hci_evt_desc_tab
0x0001ed58 D hci_evt_le_desc_tab
0x0001ee00 D aes_cmac_zero
0x0001ee10 D aes_k2_salt
0x0001ee20 D aes_k3_id64
0x0001ee25 D aes_k3_salt
0x0001ee35 D aes_k4_id6
0x0001ee39 D aes_k4_salt
0x0001ee49 D one_bits
0x0001ee5a D co_sca2ppm
0x0001ee6a D co_null_bdaddr
0x0001ee70 D co_default_bdaddr
0x0001ee76 D co_rate_to_phy
0x0001ee7b D co_phy_to_rate
0x0001ee7f D co_phy_mask_to_value
0x0001ee84 D co_phy_value_to_mask
0x0001ee88 D co_rate_to_phy_mask
0x0001ee8c D BasePoint_x_256
0x0001eeac D BasePoint_y_256
0x0001eecc D maxSecretKey_256
0x0001eeec D DebugE256PublicKey_x
0x0001ef0c D DebugE256PublicKey_y
0x0001ef2c D DebugE256SecretKey
0x0001ef4c D coef_B
0x0001ef78 D bigHexP256
0x0001efa4 D veryBigHexP256
0x0001eff4 D ecc_Jacobian_InfinityPoint256
0x0001f078 D ECC_4Win_Look_up_table
0x0001f849 D rwip_priority
0x0001fc60 D uart_api
0x0001fc70 D uart_baud_map
0x0001fc88 D sector_erase_cmd
0x0001fc8c D read_id_cmd
0x0001fc90 D read_status_cmd
0x0001fc94 D read_status_h_cmd
0x0001fc98 D write_enable_cmd
0x0001fc9c D write_status_cmd
0x0001fca0 D write_disable_cmd
0x0001fca4 D deep_sleep_cmd
0x0001fca8 D wakeup_cmd
0x0001fcac D write_volatile_enable_cmd
0x0001fcb0 D block_erase_cmd
0x0001fcb4 D write_cmd
0x0001fcb8 D read_cmd
0x0001fcbc D system_clk_map
0x0001fcc0 D system_regs
0x0001fcc4 D frspim_reg
0x20000080 D __jump_table
0x200000dc D app_storage_base_addr_index
0x200000dd D app_boot_param
0x200000e4 D aa_gen
0x200000ec D lld_exp_sync_pos_tab
0x20000105 D hci_ext_host
0x20000128 D sch_slice_params
0x20000130 D ecc_env
0x20000138 D ke_task_env
0x2000013c D rwip_param
0x20000144 D __stdout
0x20000148 D __stdin
0x2000014c D error
0x20000150 D critical_sec_cnt
0x20000154 D unloaded_area
0x20000158 D rf_init
0x2000015c D gapc_get_conidx
0x20000160 D appm_init
0x20000164 D rwble_hl_init
0x20000168 D svc_exception_handler
0x2000016c D low_power_save_entry
0x20000170 D low_power_restore_entry
0x20000174 D user_entry_before_sleep
0x20000178 D user_entry_after_sleep
0x2000017c D rtos_entry
0x20000180 D ke_task_handler_get_user
0x20000184 D em_ble_base_address_table_0
0x20000186 D em_ble_base_address_table_1
0x20000188 D em_ble_base_address_table_2
0x2000018a D em_ble_base_address_table_3
0x2000018c D em_ble_base_address_table_4
0x2000018e D em_ble_base_address_table_5
0x20000190 D em_ble_base_address_table_6
0x20000192 D em_ble_base_address_table_7
0x20000194 D em_ble_base_address_table_8
0x20000196 D em_ble_base_address_table_9
0x20000198 D em_ble_base_address_table_10
0x2000019c D qspi_ctrl
0x200001ac D pmu_calibration_isr_count
0x200001b0 D pxCurrentTCB
0x20000210 D app_user_rtos_wait_wakeup_end
0x20000254 D llc_env
0x200002c4 D lld_env
0x2000040c D lld_con_env
0x200004bc D lld_sync_env
0x20000514 D llm_env
0x200006e4 D hci_env
0x20000918 D aes_env
0x20000944 D ke_env
0x200009b4 D rwip_rf
0x200009e8 D rwip_env
0x20000a00 D low_power_store_addr
0x20000a34 D low_power_restore_remap
0x40004000 N __initial_sp

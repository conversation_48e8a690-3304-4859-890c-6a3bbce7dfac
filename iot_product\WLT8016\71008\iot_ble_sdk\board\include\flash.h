#ifndef __FLASH_H__
#define __FLASH_H__

#include "sys_utils.h"

/**
 * @brief   WLT8828FA32 Flash 地址说明
 *  whole_flash_address         : 0 <= address < 0x80000                   (0 - 512K)               //整个Flash的地址
 *  imageA_address              : 0 <= address < 0x26000                   (0 - 152K)               //A区地址（0x26000由user_image_size函数设置）
 *  imageB_address              : 0x26000 <= address < 0x4C000             (152K - 304K)            //B区地址（0x4C000由0x26000*2得到）
 *  unable_to_operate_address   : 0x79000 <= address < 0x7B000             (484K - 492K)            //不可操作地址
 *                              : 0x7D000 <= address < 0x80000             (500K - 512K)            //不可操作地址
 *  can_operate_address         : 0x4C000 <= address < 0x79000             (304K - 484K)            //可操作地址
 *                              : 0x7B000 <= address < 0x7D000             (492K - 500K)            //可操作地址
*/

enum
{
    DEV_FLASH_OPERA_SUCCESS = 0,    //Flash操作成功
    DEV_FLASH_OPERA_Fail,           //Flash操作失败
};

/**
 * @brief   FLASH 初始化
 * 
 * @param   空
 * 
 * @return  空
 */
void dev_flash_init(void);

/**
 * @brief   FLASH 写数据
 * 
 * @param   offset                  - flash 地址
 * @param   length                  - 写数据长度
 * @param   buffer                  - 写数据buffer首地址
 * 
 * @return  DEV_FLASH_OPERA_SUCCESS：flash写入地址不在可操作地址范围内
 *          DEV_FLASH_OPERA_Fail：flash写入地址在可操作地址范围内
 */
int dev_flash_write(uint32_t offset, uint32_t length, uint8_t *buffer);

/**
 * @brief   FLASH 读数据
 * 
 * @param   offset                  - flash 地址
 * @param   length                  - 读数据长度
 * @param   buffer                  - 读数据buffer首地址
 * 
 * @return  空
 */
void dev_flash_read(uint32_t offset, uint32_t length, uint8_t *buffer);

/**
 * @brief   FLASH 擦除
 * 
 * @param   offset                  - flash 地址
 * @param   size                    - 擦除区域大小（必须是0x1000的整数倍）
 * 
 * @return  DEV_FLASH_OPERA_SUCCESS：flash擦除地址不在可操作地址范围内
 *          DEV_FLASH_OPERA_Fail：flash擦除地址在可操作地址范围内
 */
int dev_flash_erase(uint32_t offset, uint32_t size);

/**
 * @brief   FLASH 读取ID
 * 
 * @param   空
 * 
 * @return  FLASH ID
 */
uint32_t dev_flash_read_id(void);
#endif /* __FLASH_H__ */

